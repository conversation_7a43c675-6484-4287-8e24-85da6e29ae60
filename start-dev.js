#!/usr/bin/env node

const { spawn } = require('child_process');
const os = require('os');

console.log('🚀 启动 React Native 开发服务器...');
console.log('');

// 获取本机IP地址
function getIPAddress() {
  const interfaces = os.networkInterfaces();
  for (let devName in interfaces) {
    const iface = interfaces[devName];
    for (let i = 0; i < iface.length; i++) {
      let alias = iface[i];
      if (
        alias.family === 'IPv4' &&
        alias.address !== '127.0.0.1' &&
        !alias.internal
      ) {
        return alias.address;
      }
    }
  }
  return 'localhost';
}

const ip = getIPAddress();
console.log(`📱 本机IP地址: ${ip}`);
console.log(`🌐 Metro 服务器将在 http://${ip}:8081 启动`);
console.log('');

// 尝试不同的启动方式
const startCommands = [
  'npx @react-native-community/cli start --reset-cache',
  'npx react-native start --reset-cache',
  'node node_modules/@react-native-community/cli/build/bin.js start --reset-cache',
  'node node_modules/react-native/local-cli/cli.js start --reset-cache'
];

let currentIndex = 0;

function tryStart() {
  if (currentIndex >= startCommands.length) {
    console.error('❌ 所有启动方式都失败了');
    console.log('');
    console.log('💡 建议手动尝试以下命令：');
    console.log('   1. yarn add @react-native-community/cli --dev');
    console.log('   2. npx @react-native-community/cli start');
    console.log('');
    process.exit(1);
  }

  const command = startCommands[currentIndex];
  console.log(`⚡ 尝试启动方式 ${currentIndex + 1}: ${command}`);
  
  const [cmd, ...args] = command.split(' ');
  const child = spawn(cmd, args, {
    stdio: 'inherit',
    shell: true
  });

  child.on('error', (error) => {
    console.log(`❌ 启动方式 ${currentIndex + 1} 失败: ${error.message}`);
    currentIndex++;
    setTimeout(tryStart, 1000);
  });

  child.on('exit', (code) => {
    if (code !== 0) {
      console.log(`❌ 启动方式 ${currentIndex + 1} 退出，代码: ${code}`);
      currentIndex++;
      setTimeout(tryStart, 1000);
    }
  });
}

// 开始尝试启动
tryStart();
