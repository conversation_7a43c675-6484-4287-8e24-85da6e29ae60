export const tasks = {
	"ret": 0,
	"msg": null,
	"data": {
		"countDownMills": 31663066,
		"summaryTask": {
			"id": 301,
			"taskId": null,
			"title": "累计完成10次任务额外得80积分",
			"worth": 80,
			"taskType": 99,
			"subTaskType": 0,
			"periodPattern": 1,
			"desc": "",
			"logo": null,
			"guideText": null,
			"guideLink": null,
			"contextMap": {
				"0": "去完成",
				"1": "领取",
				"2": "已领取"
			},
			"code": 0,
			"order": -100000009,
			"condition": 10,
			"status": 0,
			"progress": 1,
			"taskLabel": null,
			"statusText": "去完成",
			"tag": null,
			"stepNo": 1,
			"stepType": 1,
			"stepInfos": [{
				"stepNo": 1,
				"stepStatus": 0,
				"statusText": "去完成",
				"condition": 3,
				"worth": 10,
				"desc": "积分",
				"expireTime": null,
				"productId": null,
				"awardExpireTime": null,
				"worthUnit": null,
				"awardCover": null
			}, {
				"stepNo": 2,
				"stepStatus": 0,
				"statusText": "去完成",
				"condition": 6,
				"worth": 20,
				"desc": "积分",
				"expireTime": null,
				"productId": null,
				"awardExpireTime": null,
				"worthUnit": null,
				"awardCover": null
			}, {
				"stepNo": 3,
				"stepStatus": 0,
				"statusText": "去完成",
				"condition": 10,
				"worth": 50,
				"desc": "积分",
				"expireTime": null,
				"productId": null,
				"awardExpireTime": null,
				"worthUnit": null,
				"awardCover": null
			}],
			"stepStatusMap": {
				"1": 0,
				"2": 0,
				"3": 0
			},
			"assisters": null,
			"showStart": 1679645496000,
			"showEnd": 1735632697000,
			"drawHidden": null,
			"activityLogo": null,
			"assiterCount": null,
			"targetId": null
		},
		"taskItems": [{
			"id": 302,
			"taskId": null,
			"title": "去看免费小说领现金",
			"worth": 80,
			"taskType": 12,
			"subTaskType": 0,
			"periodPattern": 1,
			"desc": "新人送1元，看书即可提现",
			"logo": null,
			"guideText": null,
			"guideLink": "iting://open?msg_type=94&bundle=rn_novel_app",
			"contextMap": {
				"0": "去完成",
				"1": "领取",
				"2": "已领取"
			},
			"code": 0,
			"order": -101015490,
			"condition": 1,
			"status": 0,
			"progress": 0,
			"taskLabel": null,
			"statusText": "去完成",
			"tag": null,
			"stepNo": 0,
			"stepType": 0,
			"stepInfos": [],
			"stepStatusMap": {},
			"assisters": null,
			"showStart": 1680192000000,
			"showEnd": 1767126539000,
			"drawHidden": null,
			"activityLogo": null,
			"assiterCount": null,
			"targetId": null
		}, {
			"id": 999,
			"taskId": null,
			"title": "下载抖音极速版领现金",
			"worth": 100,
			"taskType": 12,
			"subTaskType": 0,
			"periodPattern": 1,
			"desc": "清动浏览商品30秒，立得100金币",
			"logo": null,
			"guideText": null,
			"guideLink": "iting://open?msg_type=94&bundle=rn_douyin_app",
			"contextMap": {
				"0": "去完成",
				"1": "领取",
				"2": "已领取"
			},
			"code": 0,
			"order": -101015491,
			"condition": 1,
			"status": 0,
			"progress": 0,
			"taskLabel": null,
			"statusText": "去完成",
			"tag": null,
			"stepNo": 0,
			"stepType": 0,
			"stepInfos": [],
			"stepStatusMap": {},
			"assisters": null,
			"showStart": 1680192000000,
			"showEnd": 1767126539000,
			"drawHidden": null,
			"activityLogo": null,
			"assiterCount": null,
			"targetId": null
		}, {
			"id": 345,
			"taskId": null,
			"title": "0元学配音 挑战兼职",
			"worth": 200,
			"taskType": 100,
			"subTaskType": 0,
			"periodPattern": 0,
			"desc": "0基础可学 无需露脸 接单兼职",
			"logo": null,
			"guideText": null,
			"guideLink": "https://m.ximalaya.com/gatekeeper/lms-sale-page?expGroupId=25&xbSource=54792",
			"contextMap": {
				"0": "去参与",
				"1": "领取",
				"2": "已完成"
			},
			"code": 0,
			"order": -101007312,
			"condition": 1,
			"status": 0,
			"progress": 0,
			"taskLabel": null,
			"statusText": "去参与",
			"tag": null,
			"stepNo": 0,
			"stepType": 0,
			"stepInfos": [],
			"stepStatusMap": {},
			"assisters": null,
			"showStart": 1686819186000,
			"showEnd": 1735660799000,
			"drawHidden": null,
			"activityLogo": null,
			"assiterCount": null,
			"targetId": null
		}, {
			"id": 177,
			"taskId": null,
			"title": "浏览会员频道",
			"worth": 10,
			"taskType": 102,
			"subTaskType": 1,
			"periodPattern": 1,
			"desc": "可领10积分",
			"logo": null,
			"guideText": null,
			"guideLink": "iting://open?msg_type=99&_ka=1?utm_source=jifencenter",
			"contextMap": {
				"0": "去完成",
				"1": "领取",
				"2": "已领取"
			},
			"code": 99,
			"order": -101002857,
			"condition": 1,
			"status": 0,
			"progress": 0,
			"taskLabel": null,
			"statusText": "去完成",
			"tag": null,
			"stepNo": 0,
			"stepType": 0,
			"stepInfos": [],
			"stepStatusMap": {},
			"assisters": null,
			"showStart": 0,
			"showEnd": 1709205733000,
			"drawHidden": null,
			"activityLogo": null,
			"assiterCount": null,
			"targetId": null
		}, {
			"id": 329,
			"taskId": null,
			"title": "浏览“换会员”页面，海量会员等你换",
			"worth": 10,
			"taskType": 102,
			"subTaskType": 1,
			"periodPattern": 1,
			"desc": "腾讯视频、bilibili....",
			"logo": null,
			"guideText": null,
			"guideLink": "iting://open?msg_type=94&bundle=rn_benefits_conversion&goodsShelfName=换会员&goodsShelfId=2",
			"contextMap": {
				"0": "去完成",
				"1": "领取",
				"2": "已完成"
			},
			"code": 99,
			"order": -101000959,
			"condition": 1,
			"status": 0,
			"progress": 0,
			"taskLabel": null,
			"statusText": "去完成",
			"tag": null,
			"stepNo": 0,
			"stepType": 0,
			"stepInfos": [],
			"stepStatusMap": {},
			"assisters": null,
			"showStart": 1666627200000,
			"showEnd": 1717154574000,
			"drawHidden": null,
			"activityLogo": null,
			"assiterCount": null,
			"targetId": null
		}, {
			"id": 376,
			"taskId": null,
			"title": "儿童暑期必听榜",
			"worth": 10,
			"taskType": 102,
			"subTaskType": 1,
			"periodPattern": 1,
			"desc": "精品内容专辑限免中",
			"logo": null,
			"guideText": null,
			"guideLink": "https://pages.ximalaya.com/mkt/act/d72426f049b1d322?utm_source=qiandao",
			"contextMap": {
				"0": "去收听",
				"1": "领取",
				"2": "已领取"
			},
			"code": 99,
			"order": -101000910,
			"condition": 1,
			"status": 0,
			"progress": 0,
			"taskLabel": null,
			"statusText": "去收听",
			"tag": null,
			"stepNo": 0,
			"stepType": 0,
			"stepInfos": [],
			"stepStatusMap": {},
			"assisters": null,
			"showStart": 1690538793000,
			"showEnd": 1693497600000,
			"drawHidden": null,
			"activityLogo": null,
			"assiterCount": null,
			"targetId": null
		}, {
			"id": 96,
			"taskId": null,
			"title": "分享节目",
			"worth": 20,
			"taskType": 3,
			"subTaskType": 2,
			"periodPattern": 1,
			"desc": "领20积分",
			"logo": "https://fdfs.xmcdn.com/storages/8698-audiofreehighqps/AF/E5/CMCoOSMEVwY-AAADfwChqChq.png",
			"guideText": null,
			"guideLink": "iting://open?msg_type=139&share_point=20",
			"contextMap": {
				"0": "去分享",
				"1": "领取",
				"2": "已领取"
			},
			"code": 0,
			"order": -101000041,
			"condition": 1,
			"status": 0,
			"progress": 0,
			"taskLabel": null,
			"statusText": "去分享",
			"tag": null,
			"stepNo": 0,
			"stepType": 0,
			"stepInfos": [],
			"stepStatusMap": {},
			"assisters": null,
			"showStart": 0,
			"showEnd": 0,
			"drawHidden": null,
			"activityLogo": null,
			"assiterCount": null,
			"targetId": null
		}, {
			"id": 294,
			"taskId": null,
			"title": "投月票得积分",
			"worth": 20,
			"taskType": 100,
			"subTaskType": 0,
			"periodPattern": 1,
			"desc": "可得20积分",
			"logo": null,
			"guideText": null,
			"guideLink": "iting://open?msg_type=365",
			"contextMap": {
				"0": "去投票",
				"1": "领取",
				"2": "已领取"
			},
			"code": 0,
			"order": -101000033,
			"condition": 1,
			"status": 0,
			"progress": 0,
			"taskLabel": null,
			"statusText": "去投票",
			"tag": null,
			"stepNo": 0,
			"stepType": 0,
			"stepInfos": [],
			"stepStatusMap": {},
			"assisters": null,
			"showStart": 1678441403000,
			"showEnd": 1711878205000,
			"drawHidden": null,
			"activityLogo": null,
			"assiterCount": null,
			"targetId": null
		}, {
			"id": 342,
			"taskId": null,
			"title": "订阅一张播客专辑",
			"worth": 20,
			"taskType": 100,
			"subTaskType": 0,
			"periodPattern": 1,
			"desc": "每日订阅播客专辑，可得20积分",
			"logo": null,
			"guideText": null,
			"guideLink": "iting://open?msg_type=146&album_id=76291913",
			"contextMap": {
				"0": "去完成",
				"1": "领取",
				"2": "已领取"
			},
			"code": 0,
			"order": -101000029,
			"condition": 1,
			"status": 0,
			"progress": 0,
			"taskLabel": null,
			"statusText": "去完成",
			"tag": null,
			"stepNo": 0,
			"stepType": 0,
			"stepInfos": [],
			"stepStatusMap": {},
			"assisters": null,
			"showStart": 1686032080000,
			"showEnd": 1735660799000,
			"drawHidden": null,
			"activityLogo": null,
			"assiterCount": null,
			"targetId": null
		}, {
			"id": 147,
			"taskId": null,
			"title": "收听直播5分钟",
			"worth": 30,
			"taskType": 3,
			"subTaskType": 5,
			"periodPattern": 1,
			"desc": "每日收听任意直播5分钟，可得30积分",
			"logo": null,
			"guideText": null,
			"guideLink": "iting://open?msg_type=330&type=1",
			"contextMap": {
				"0": "去完成",
				"1": "领取",
				"2": "已领取"
			},
			"code": 0,
			"order": -101000006,
			"condition": 1,
			"status": 0,
			"progress": 0,
			"taskLabel": null,
			"statusText": "去完成",
			"tag": null,
			"stepNo": 0,
			"stepType": 0,
			"stepInfos": [],
			"stepStatusMap": {},
			"assisters": null,
			"showStart": 1665417600000,
			"showEnd": 1699581600000,
			"drawHidden": null,
			"activityLogo": null,
			"assiterCount": null,
			"targetId": null
		}, {
			"id": 203,
			"taskId": null,
			"title": "收听得积分",
			"worth": 50,
			"taskType": 102,
			"subTaskType": 1,
			"periodPattern": 1,
			"desc": "",
			"logo": null,
			"guideText": null,
			"guideLink": "iting://open?msg_type=139",
			"contextMap": {
				"0": "去完成",
				"1": "领取",
				"2": "已完成"
			},
			"code": 99,
			"order": -99999989,
			"condition": 1,
			"status": 0,
			"progress": 0,
			"taskLabel": null,
			"statusText": "去完成",
			"tag": null,
			"stepNo": 0,
			"stepType": 0,
			"stepInfos": [],
			"stepStatusMap": {},
			"assisters": null,
			"showStart": 1666627200000,
			"showEnd": 1730304000000,
			"drawHidden": null,
			"activityLogo": null,
			"assiterCount": null,
			"targetId": null
		}],
		"ruleCheckResult": {
			"tags": ["pc&1&true&false&false&false"],
			"tagsForFrontend": ["pc", "1", "true", "false", "false", "false"]
		},
		"activityStatus": 1,
		"msg": null
	},
	"context": null,
	"activityContext": {
		"activityBasicInfo": {
			"aid": 112,
			"title": "积分中心9.0新版任务列表",
			"logo": null,
			"extraInfo": {
				"videoTaskInfo": {
					"positionId": 254,
					"positionName": "integral_center_inspire_video"
				}
			},
			"isParticipateBefore": true
		},
		"ruleParseInfo": [{
			"ruleType": 3,
			"tag": "pc"
		}, {
			"ruleType": 16,
			"tag": "1"
		}, {
			"ruleType": 24,
			"tag": "true"
		}, {
			"ruleType": 24,
			"tag": "false"
		}, {
			"ruleType": 24,
			"tag": "false"
		}, {
			"ruleType": 24,
			"tag": "false"
		}]
	}
}