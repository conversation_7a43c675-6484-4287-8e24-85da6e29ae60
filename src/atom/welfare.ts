import { atom } from 'jotai';
import { queryDrawInfo } from 'services/welfare';
interface WelfareState {
  coins: number;
  cash: number;
  enableCash: boolean;
  isLoading: boolean;
  error: string | null;
}

interface TabsState {
  tabs: string[];
  activeTab?: 'COINS' | 'POINTS';
  isLoading: boolean;
  error: string | null;
}

const initialState: WelfareState = {
  coins: 0,
  cash: 0,
  enableCash: false,
  isLoading: false,
  error: null,
};

const initialTabsState: TabsState = {
  tabs: [],
  activeTab: undefined,
  isLoading: true,
  error: null,
};

export const balanceAtom = atom(initialState);
export const tabsAtom = atom(initialTabsState);

export const updateWelfareAtom = atom(
  (get) => ({ balance: get(balanceAtom), tabs: get(tabsAtom) }),
  async (get, set) => {
    const currentBalance = get(balanceAtom);
    const currentTabs = get(tabsAtom);
    
    set(balanceAtom, { ...currentBalance, isLoading: true, error: null });
    set(tabsAtom, { ...currentTabs, isLoading: true, error: null });

    try {
      const response = await queryDrawInfo();
      if (response && response.ret === 0 && response.data.success) {
        const { drawInfo, tabs, activateTab } = response.data;
        if (drawInfo) {
          set(balanceAtom, {
            coins: drawInfo.coins,
            cash: drawInfo.cash,
            enableCash: drawInfo.enableCash,
            isLoading: false,
            error: null,
          });
          set(tabsAtom, {
            tabs,
            activeTab: activateTab,
            isLoading: false,
            error: null,
          });
        } else {
          throw new Error('No draw info available');
        }
      } else {
        throw new Error('Failed to fetch welfare info');
      }
    } catch (error) {
      console.log('debug_draw_info_error', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      set(balanceAtom, { ...currentBalance, isLoading: false, error: errorMessage });
      set(tabsAtom, { ...currentTabs, isLoading: false, error: errorMessage });
    }
  }
); 