import { Toast } from "@xmly/rn-sdk";
import { isAndroid, isIOS } from "@xmly/rn-utils";
import { FallbackReqType } from "constants/ad";
import customReportError from "utilsV2/customReportError";
import { listenEarnRewardCoin } from "utilsV2/native";
import log from "./log";

interface WatchAdParams {
  positionName?: string;
  slotId?: number;
  extInfo?: string;
  successInfo?: string;
  failInfo?: string;
  otherParams?: Record<string, any>;
  rewardVideoStyle?: number; // 0: RN发放奖励, 1: 客户端发放奖励
  sourceName?: string;
  rewardType?: number;
  coins?: number;
  configPositionName?: string
}

export interface WatchAdResult {
  success: boolean;
  adId?: number;
  adResponseId?: number;
  encryptType?: string;
  ecpm?: string;
  fallbackReq?: FallbackReqType; // 0: 正常, 1: 兜底
}

export default async function watchAd(params: WatchAdParams): Promise<WatchAdResult> { // 看视频, rn发放奖励
  const {
    positionName = 'incentive_welfare',
    slotId = 307,
    extInfo,
    successInfo,
    failInfo,
    otherParams,
    rewardVideoStyle = 0,
    sourceName,
    rewardType,
    coins
  } = params;
  try {
    const options = {
      positionName,
      slotId,
      extInfo: JSON.stringify({ ...JSON.parse(extInfo || '{}'), sourceName }), // 透传给服务端的数据
      rewardVideoStyle,
      otherParams,
      sourceName,
      rewardType,
      coins
    };
    const res = await listenEarnRewardCoin(options);
    console.info('debug_watchAd_res', res);
    // iOS端clientCode的返回值为'', 则成功；若失败，会走catch
    // android端clientCode的返回值为'success', 则成功；若失败，会走catch
    if (
      (res.clientCode === '' && isIOS) ||
      (String(res.clientCode) === '10003' && isAndroid)
      || res?.clientCode === 'fallBackReq' && isIOS // 旧版iOS客户端超时兜底逻辑，会直接发放奖励
      || (String(res.clientCode) === '10004' && isAndroid) // 旧版android客户端超时兜底逻辑，会直接发放奖励
    ) {
      successInfo && Toast.info(successInfo);
      return {
        success: true,
        adId: res?.adId,
        adResponseId: res?.adResponseId,
        encryptType: res?.encryptType,
        ecpm: res?.ecpm,
        fallbackReq: res.fallbackReq ? Number(res.fallbackReq) : FallbackReqType.NORMAL,
      };
    } else {
      customReportError({ source: 'watchAd.invalid_result', error: res?.toString() === '[object Object]' ? JSON.stringify(res) : res })
      failInfo && Toast.info(failInfo);
      return {
        success: false,
      };
    }
  } catch (err) {
    console.info('debug_listenEarnRewardCoin_err', err);
    customReportError({ source: 'watchAd.catch', error: err })
    failInfo && Toast.info(failInfo)
    return {
      success: false,
    };
  }
}