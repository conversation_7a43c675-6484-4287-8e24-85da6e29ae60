import React, { useEffect, useMemo, useState } from 'react'
import { NativeEventEmitter, NativeModules, Appearance, Platform } from 'react-native'
import { ThemeProvider } from 'styled-components'
import rnEnv from '../rnEnv'
import { ThemeContext } from './contextV2/themeContext'
import { UserInfoContext } from './contextV2/userInfoContext'
import userInfoDetail from './modulesV2/userInfoDetail'
import { NativeInfo } from './typesV2/nativeInfo'
import UserLoginStatus from './typesV2/userLoginStatus'
import themes from './themeV2/index'
import { EnvName } from './typesV2/common'
import CookieConfig from './utilsV2/cookieConfig'

import { initialWindowMetrics, SafeAreaProvider } from 'react-native-safe-area-context'
import GlobalEventEmitter from './utilsV2/globalEventEmitter'
import AppRootContent from './AppContent'
import AppRootSkeleton from './AppContent/AppRootSkeleton'
import nativeInfoModule from './modulesV2/nativeInfoModule'
import { DarkModeContext } from './contextV2/darkModeContext'
import GrayScaleProvider from './componentsV2/common/GrayScaleProvider'
// import MonthlyTicketGuideProvider from './componentsV2/common/MonthlyTicketGuideProvider'
import ChannelUndertakeHOC from './componentsV2/common/ChannelUndertakeHOC'
import { setAndroidFullScreen, setAndroidStatusBar } from '@xmly/rn-utils'
import noLoginCheckInStorage from './storageV2/noLoginCheckInStorage'
import dayjs from 'dayjs'
import { Provider } from 'react-redux'
import { store } from './store'
import { useSetAtom } from 'jotai'
import { themeAtom } from './atom/theme'
import Wallet from 'pages/Wallet'

// 提前设置用户的登录状态，如果提前设置过，就不设置
const prevSetUserLoginInfo = (props: NativeInfo) => {
  if (props?.initData?.account?.uid && !userInfoDetail.hasPrevSet()) {
    console.log('prevSetUserLoginInfo 🚒🚒🚒🚒🚒🚒🚒🚒')
    userInfoDetail.setDetail(props?.initData?.account)
  }
}

const AccountEventEmitter = new NativeEventEmitter(NativeModules.Account)

const setDarkMode = () => {
  Platform.OS === 'ios' && NativeModules.XMDarkMode.setDarkModeEnable(true)
}

const Root: React.FC<NativeInfo> = (props) => {
  const [contentReady, setContentReady] = useState(false)
  rnEnv.setEnv(props.initData.env.envType)
  nativeInfoModule.setInfo(props)
  prevSetUserLoginInfo(props)

  const [isReady, setIsReady] = useState(rnEnv.getFormatEnvType(props.initData.env.envType) === EnvName.prod)
  const [theme, setTheme] = useState(Appearance.getColorScheme() === 'light' ? themes.light : themes.dark)
  const setThemeAtom = useSetAtom(themeAtom);
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [loginStatus, setLoginStatus] = useState<UserLoginStatus>(props?.initData?.account?.isLogin ? UserLoginStatus.login : UserLoginStatus.notLogin)
  const [showNoLoginCheckInPop, setShowNoLoginCheckInPop] = useState<boolean>(false)
  const inWallet = props?.route === 'wallet';

  /** 设置安卓端全屏和状态栏 */
  const setAndroidProfile = async () => {
    try {
      // /** 安卓设置全屏 */
      Platform.OS === 'android' && setAndroidFullScreen(true)
      Platform.OS === 'android' && setAndroidStatusBar(isDarkMode ? false : true)
    } catch (err) {
      console.log(err)
    }
  }

  useEffect(() => {
    setDarkMode()
    setAndroidProfile()
  }, [])

  useEffect(() => {
    const appContentReadyListener = GlobalEventEmitter.addListener('appContentReady', () => {
      setContentReady(true)
    })
    const appContentLoadingListener = GlobalEventEmitter.addListener('appContentLoading', () => {
      setContentReady(false)
    })

    return () => {
      appContentReadyListener.remove()
      appContentLoadingListener.remove()
    }
  }, [])

  useEffect(() => {
    if (props?.initData?.account?.uid) {
      userInfoDetail.setDetail(props?.initData?.account)
    }
    handleThemeChange()
    Appearance.addChangeListener(handleThemeChange)
    return () => {
      Appearance.removeChangeListener(handleThemeChange)
    }
  }, [])

  const handleThemeChange = () => {
    const colorScheme: 'light' | 'dark' = Appearance.getColorScheme() ?? 'light'
    setTheme(themes[colorScheme] ?? themes.light)
    setThemeAtom(Appearance.getColorScheme() ?? 'light');
    setIsDarkMode(colorScheme === 'dark' ? true : false)
  }

  const userInfoContextValue = useMemo(() => {
    return {
      loginStatus,
    }
  }, [loginStatus])

  const getUserLoginStatus = async () => {
    try {
      const res = await NativeModules.Account.getUserInfo()
      const uid = res.uid;
      setLoginStatus(uid ? UserLoginStatus.login : UserLoginStatus.notLogin)
      userInfoDetail.setDetail(uid ? res : null)
      CookieConfig.setCookie({ uid: res.uid, token: res.token }) //设置cookie
    } catch (err) {
      console.log(err)
      setLoginStatus(UserLoginStatus.notLogin)
      userInfoDetail.setDetail(null)
    }
  }

  useEffect(() => {
    getUserLoginStatus()
    setIsReady(true)
    if (!inWallet) {
      handleNoLoginStatusRecord()
    }
    return () => {
      CookieConfig.setCookie({ uid: '', token: '' }) //清除cookie
    }
  }, [inWallet])

  const handleLogin = async () => {
    try {
      const res = await NativeModules.Account.getUserInfo()
      if (res?.uid) {
        userInfoDetail.setDetail(res)
      } else {
        userInfoDetail.setDetail(null)
      }
    } catch (err) {
      userInfoDetail.setDetail(null)
    }

    setLoginStatus(UserLoginStatus.login)
  }

  useEffect(() => {
    const listener = AccountEventEmitter.addListener('onLogin', handleLogin)
    return () => {
      listener.remove()
    }
  }, [])

  const handleNoLoginStatusRecord = async () => {
    const recordDate = await noLoginCheckInStorage.get()
    console.log('recordDate:', recordDate)
    if (recordDate) {
      // 已经记录，则判断是否是今天，如果不是，则更新
      if (dayjs().isSame(recordDate, 'day')) {
        // 今天已经记录，未登录签到不展示
        console.log('=====今日已被记录===:', recordDate)
        setShowNoLoginCheckInPop(false)
      } else {
        // 非今日记录，未登录签到需要展示，并且更新为今日记录
        setShowNoLoginCheckInPop(true)
      }
    } else {
      // 没有记录，未登录签到需要展示，添加记录
      setShowNoLoginCheckInPop(true)
    }
    noLoginCheckInStorage.set()
  }

  const renderContent = (loginStatus: UserLoginStatus) => {
    if (loginStatus === UserLoginStatus.UNSET) return null

    // 🚨这个写法是为了当登录状态变化的时候，所有的子组件可以重新执行各自的生命周期函数
    return (
      <GrayScaleProvider key={loginStatus}>
        <ChannelUndertakeHOC srcChannel={props.srcChannel}>
          {/* <MonthlyTicketGuideProvider> */}
          <AppRootContent {...props} isLogin={loginStatus === UserLoginStatus.login} showNoLoginCheckInPop={showNoLoginCheckInPop} isDarkMode={isDarkMode} key={loginStatus} />
          {/* </MonthlyTicketGuideProvider> */}
        </ChannelUndertakeHOC>
      </GrayScaleProvider>
    )
  }

  // ready 之后渲染最终页面内容
  if (isReady) {
    return (
      <SafeAreaProvider initialMetrics={initialWindowMetrics}>
        <ThemeContext.Provider value={theme}>
          <ThemeProvider theme={theme}>
            <DarkModeContext.Provider value={{ isDarkMode }}>
              <UserInfoContext.Provider value={userInfoContextValue}>
                <Provider store={store}>
                  {inWallet ?
                    <>
                      {/* {isIOS ? <StatusBar barStyle={isDarkMode ? 'light-content' : 'dark-content'} /> : null} */}
                      <Wallet />
                    </>
                    :
                    <>
                      {!contentReady ? <AppRootSkeleton /> : null}
                      {renderContent(loginStatus)}
                    </>
                  }
                </Provider>
              </UserInfoContext.Provider>
            </DarkModeContext.Provider>
          </ThemeProvider>
        </ThemeContext.Provider>
      </SafeAreaProvider>
    )
  }

  return null
}

export default Root
