export default function getUrlToOpen(
  url: string,
  options?: { xmlyAsIting?: boolean; asH5?: boolean; encodeTwice?: boolean }
) {
  if (!url) return ''
  try {
    const addDecodeOnce = url.includes('&decodeOnce=1')
    const judgeXMLY = options && options.xmlyAsIting
    const asH5 = options && options.asH5
    const encodeTwice = options && options.encodeTwice
    const pretreatmentH5Link = !encodeTwice
      ? encodeURIComponent(url)
      : encodeURIComponent(encodeURIComponent(url))
    const H5LinkIting = `iting://open?msg_type=14&url=${pretreatmentH5Link}&_ka=1${
      addDecodeOnce ? '&decodeOnce=1' : ''
    }`
    if (asH5) {
      return H5LinkIting
    }

    const ifIting =
      url.indexOf('iting') !== -1 ||
      (judgeXMLY && url.indexOf('xmly://') !== -1)
    const toUrl = ifIting ? url : H5LinkIting
    console.log({ toUrl })
    return toUrl
  } catch (err) {
    return ''
  }
}
