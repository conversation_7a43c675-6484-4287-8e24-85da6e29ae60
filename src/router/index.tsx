import React, { useEffect, useState, useRef } from 'react'
import LazyScreen from 'react-navigation-lazy-screen'
import { DefaultTheme, NavigationContainer, NavigationState } from '@react-navigation/native'
import {
  CardStyleInterpolators,
  createStackNavigator,
} from '@react-navigation/stack'
import { useSetAtom, useAtomValue } from 'jotai'
import { updateWelfareAtom, tabsAtom } from '../atom/welfare'
import { RootStackParamList } from './type'
import { Appearance } from 'react-native'
import { useContext } from 'react'
//@ts-ignore
import HomeRoute from './HomeRoute'
import { isHarmony } from '../../rnEnv'
import { NativeInfoContext } from 'contextV2/nativeInfoContext'
import GlobalEventEmitter from 'utilsV2/globalEventEmitter'

const RootStack = createStackNavigator<RootStackParamList>()
const Theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    background: Appearance.getColorScheme() === 'light' ? '#f8f8f8' : '#121212',
  },
}

const Router = () => {
  const [isReady, setIsReady] = useState(false);
  const updateWelfare = useSetAtom(updateWelfareAtom);
  const { activeTab } = useAtomValue(tabsAtom);
  const nativeInfo = useContext(NativeInfoContext);
  // 判断是否强制进入金币中心
  const forceEnterCoinCenter = isHarmony || nativeInfo?.coin;
  const initialRouteName = forceEnterCoinCenter ? 'CoinCenter' : (activeTab === 'COINS' ? 'CoinCenter' : 'CoinDetail');
  // const initialRouteName = 'CoinDetail';
  
  // 跟踪上一个路由名称
  const prevRouteNameRef = useRef<string | null>(null);

  useEffect(() => {
    const init = async () => {
      await updateWelfare();
      setIsReady(true);
    };
    init();
    GlobalEventEmitter.emit('appContentReady');
  }, []);

  // 处理导航状态变化
  const handleNavigationStateChange = (state: NavigationState | undefined) => {
    if (!state) return;
    
    const currentRouteName = state.routes[state.index]?.name || null;
    
    // 如果有上一个路由名称，且不同于当前路由
    if (prevRouteNameRef.current && 
        prevRouteNameRef.current !== currentRouteName) {
      
      // 如果切换到CoinCenter页面，则更新福利信息
      if (currentRouteName === 'CoinCenter' && prevRouteNameRef.current !== 'CoinCenter') {
        updateWelfare();
      }
    }
    
    // 更新上一个路由名称
    prevRouteNameRef.current = currentRouteName;
  };

  if (!isReady) {
    return null; // 或者返回一个加载指示器
  }

  return (
    <NavigationContainer 
      theme={Theme}
      onStateChange={handleNavigationStateChange}
    >
      {/* Screen configuration */}
      <RootStack.Navigator
        headerMode='none'
        mode='card'
        screenOptions={{
          cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
        }}
        initialRouteName={initialRouteName}
      >
        <RootStack.Screen name='Home' options={{
          gestureEnabled: false,
          animationEnabled: false,
        }}>
          {(props) => <HomeRoute {...props} />}
        </RootStack.Screen>

        <RootStack.Screen options={{
          gestureEnabled: false,
          animationEnabled: false,
        }} name='CoinCenter'>
          {(props) => <LazyScreen
            {...props}
            fallback={null}
            factory={() => import('../pages/CoinCenter')}
          />}
        </RootStack.Screen>

        <RootStack.Screen name='CommodityList'>
          {(props) => (
            <LazyScreen
              {...props}
              fallback={null}
              factory={() => import('../pagesV2/CommodityList')}
            />
          )}
        </RootStack.Screen>

        <RootStack.Screen name='CreditDetail'>
          {(props) => (
            <LazyScreen
              {...props}
              fallback={null}
              factory={() => import('../componentsV2/CreditDetail')}
            />
          )}
        </RootStack.Screen>

        <RootStack.Screen
          name="CoinDetail"
        >
          {(props) => (
            <LazyScreen
              {...props}
              fallback={null}
              factory={() => import('../pages/CoinDetail')}
            />
          )}
        </RootStack.Screen>
      </RootStack.Navigator>
    </NavigationContainer>
  );
}

export default Router;
