type UserInfoDetail = {
  hasSetPwd?: boolean
  imgUrl?: string
  isLogin?: boolean
  isNewUser?: boolean
  isVip?: boolean
  nickName?: string | null
  phone?: string | null
  token?: string
  uid?: number
}

const userInfoDetail = () => {
  let detail = {} as UserInfoDetail
  let _hasPrevSet = false // 是否预设置过
  const setDetail = (info: any) => {
    if (!_hasPrevSet) {
      _hasPrevSet = true
    }
    detail = info
  }

  const getDetail = () => {
    return detail
  }

  const hasPrevSet = () => _hasPrevSet

  return {
    hasPrevSet,
    setDetail,
    getDetail,
  }
}

export default userInfoDetail()
