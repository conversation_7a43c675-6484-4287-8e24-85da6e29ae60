import React, { useEffect, useState } from "react";
import { ScrollView, View, Text, Image } from "react-native";
import { BetterImage, Touch } from "@xmly/rn-components";
import { getStyles } from "./styles";
import { useAtomValue, useSetAtom } from "jotai";
import videoTaskThemeAtom, { VideoTaskStatus } from "./theme";
import CompletedIcon from "../common/CompletedIcon";
import TaskButton from "../common/TaskButton";
import ModuleCard from "../common/ModuleCard";
import LinearGradient from "react-native-linear-gradient";
import Title from "../common/Title";
import watchAd from "utils/watchAd";
import { updateWelfareAtom } from "atom/welfare";
import { AD_SOURCE, RewardType } from "constants/ad";
import { videoTaskAtom, writeVideoTaskAtom } from './store';
import xmlog from "utilsV2/xmlog";
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics';

const coinIcon = 'https://imagev2.xmcdn.com/storages/6940-audiofreehighqps/7B/BC/GAqh4zILsZIEAAAHpQOCKX74.png';

export default function VideoTask() {
  const theme = useAtomValue(videoTaskThemeAtom);
  const styles = getStyles(theme);
  const scrollRef = React.useRef<ScrollView>(null);
  const [rendered, setRendered] = useState(false);
  const stepWidth = 42;
  const stepPadding = 8;
  const updateWelfare = useSetAtom(updateWelfareAtom);
  const videoTasks = useAtomValue(videoTaskAtom);
  const getVideoTasks = useSetAtom(writeVideoTaskAtom);
  const list = videoTasks?.list ?? [];
  const reversedLastCompletedIndex = list.slice().reverse().findIndex((task) => task.status === VideoTaskStatus.RECEIVED);
  const lastCompletedIndex = reversedLastCompletedIndex === -1 ? -1 : list.length - 1 - reversedLastCompletedIndex;
  const currentIndex = Math.min(lastCompletedIndex + 1, list.length - 1);
  const completed = lastCompletedIndex === list.length - 1;

  useEffect(() => {
    if (currentIndex >= 0 && rendered) {
      scrollRef.current?.scrollTo({ x: currentIndex * (stepWidth + stepPadding), animated: true });
    }
  }, [rendered, currentIndex]);

  useEffect(() => {
    getVideoTasks();
  }, []);

  async function handleWatchVideo() {
    // 福利中心-看视频  点击事件
    xmlog.click(67728, 'VideoTask', { currPage: 'welfareCenter' });
    if (completed) { return }
    try {
      const res = await watchAd({
        positionName: 'incentive_welfare',
        slotId: 307,
        rewardVideoStyle: 1,
        sourceName: AD_SOURCE.AD_VIDEO,
        rewardType: RewardType.AD_VIDEO,
        coins: list[currentIndex].coins,
      });
    } catch (error) {
      console.error('debug_handleWatchVideo', error);
    }
    getVideoTasks();
    updateWelfare();
  }

  function onShow() {
    // 福利中心-看视频  控件曝光
    xmlog.event(67799, 'slipPage', { currPage: 'welfareCenter' });
  }

  return (
    <ScrollAnalyticComp
      itemKey="VideoTask"
      onShow={onShow}
    >
      <ModuleCard style={styles.container}>
        <View style={styles.head}>
          <Title title="看广告得金币" style={styles.title} />
          {completed ? <Text style={styles.completedText}>明日再来</Text> : <TaskButton text="看广告" onPress={handleWatchVideo} />}
        </View>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.steps}
          ref={scrollRef}
        >
          <View style={styles.paddingLeft} onLayout={() => setRendered(true)} />
          {videoTasks?.list?.map?.((task, index) => {
            const stepCompleted = task.status === VideoTaskStatus.RECEIVED;
            const isFuture = index > currentIndex;

            return (
              <Touch
                onPress={handleWatchVideo}
                disabled={stepCompleted}
                style={[
                  styles.step,
                  { width: stepWidth, marginRight: stepPadding },
                  stepCompleted ? styles.stepCompleted : null,
                  isFuture ? styles.stepFuture : null,
                  index === currentIndex && !stepCompleted ? styles.currentStep : null,
                ]}
                key={index}
              >
                <Image
                  source={{ uri: coinIcon }}
                  key={coinIcon}
                  // imgHeight={20}
                  // imgWidth={20}
                  style={[styles.icon, index !== currentIndex || stepCompleted ? styles.iconFuture : null]}
                />
                {stepCompleted ? (
                  <>
                    <Text style={styles.completeText}>已领取</Text>
                    <CompletedIcon style={styles.completedIcon} />
                  </>
                ) : (
                  <Text style={[styles.amount, isFuture ? styles.amountFuture : null]}>+{task.coins}</Text>
                )}
              </Touch>
            );
          })}
          <View style={styles.paddingRight} />
        </ScrollView>
        <LinearGradient
          useAngle={true}
          angle={90}
          colors={theme.shadowGradientColors}
          locations={theme.shadowGradientLocations}
          style={styles.shadow}
        />
      </ModuleCard>
    </ScrollAnalyticComp>
  );
} 