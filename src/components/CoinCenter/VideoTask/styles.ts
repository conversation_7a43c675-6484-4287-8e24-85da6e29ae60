import { StyleSheet, Platform } from "react-native";
import { px } from "utils/px";
import { darkTheme } from "./theme";
import { darkTheme as commonDarkTheme } from "../common/theme";

export const getStyles = (theme: typeof darkTheme & typeof commonDarkTheme) => StyleSheet.create({
  container: {
    marginTop: px(20),
  },
  head: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: px(16),
  },
  title: {
    fontSize: px(14),
    fontWeight: Platform.select({ ios: '500', android: 'bold' }),
    color: theme.titleColor,
    lineHeight: px(20),
  },
  steps: {
    overflow: 'scroll',
    flexDirection: 'row',
    marginTop: px(17),
  },
  paddingLeft: {
    width: px(16),
  },
  paddingRight: {
    width: px(8),
  },
  completedText: {
    fontSize: px(11),
    color: theme.completedTextColor,
  },
  step: {
    backgroundColor: theme.stepBgColor,
    borderRadius: px(3.6),
    height: px(48),
    alignItems: 'center',
  },
  currentStep: {
    borderWidth: 1,
    borderColor: '#D0AE60',
  },
  stepCompleted: {
    backgroundColor: theme.stepCompletedBgColor,
  },
  stepFuture: {
    backgroundColor: theme.stepFutureBgColor,
  },
  icon: {
    width: px(20),
    height: px(20),
    marginBottom: px(3),
    marginTop: px(6),
  },
  iconFuture: {
    // opacity: 0.5,
    opacity: 1,
  },
  amount: {
    fontFamily: 'XmlyNumber',
    fontSize: px(12),
    color: theme.amountColor,
    lineHeight: px(15),
  },
  amountFuture: {
    color: 'rgba(153, 153, 153, .5)'
  },
  completeText: {
    color: theme.completeTextColor,
    fontSize: px(10),
    lineHeight: px(15),
  },
  completedIcon: {
    marginTop: px(2),
    width: px(16),
    height: px(16),
    position: 'absolute',
    right: px(2),
    top: px(2),
  },
  shadow: {
    position: 'absolute',
    right: 0,
    bottom: 6,
    width: px(10),
    height: px(68),
  },
});
