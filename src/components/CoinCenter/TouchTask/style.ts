import { Platform, StyleSheet } from "react-native";
import { px } from "utils/px";

const getStyles = () => StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: px(105),
    right: px(12),
    width: px(66),
    height: px(66),
    zIndex: 99,
  },
  touch: {
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 99
  },
  closeBtn: {
    position: 'absolute',
    top: -px(40 - (40 - 16) / 2),
    right: -px((40 - 16) / 2),
    width: px(40),
    height: px(40),
    zIndex: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeBtnTouch: {
    width: '100%',
    height: '100%',
    backgroundColor: 'gray',
  },
  touchBtn: {
    position: 'absolute',
    bottom: 0,
    width: px(66),
    height: px(20),
    alignItems: 'center',
    justifyContent: 'center',
  },
  touchBtnText: {
    fontSize: px(11),
    color: '#FFF',
    fontFamily: 'XmlyNumber',
    fontWeight: Platform.select({ ios: '500', android: 'bold' }),
  },
  closeIcon: {
    width: px(16),
    height: px(16),
  },
  touchIcon: {
    width: px(66),
    height: px(66),
  }
});

export default getStyles;