import React, { useEffect, useRef, useState } from "react";
import { BetterImage, Text, Touch } from "@xmly/rn-components";
import { Animated, Dimensions, Image, TouchableWithoutFeedback, View } from "react-native";
import getStyles from "./style";
import useCountDown from "./hooks/useCountDownV2";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { treasureClickReport } from "./utils";
import GlobalEventEmitter from "utilsV2/globalEventEmitter";
import { HomeScrollViewEventName } from "constantsV2";
import watchAd from "utils/watchAd";
import { writeTouchTaskAtom, countdownAtom, touchTaskAtom, touchTaskVisibleAtom } from "./store";
import { AD_SOURCE, FallbackReqType, RewardType } from "constants/ad";
import { DailyTaskType } from "services/welfare";
import AnimatedLottieView from "lottie-react-native";
import { useLottieResource } from "hooks/useLottieResources";
import ConfirmButton from "../common/ConfirmButton";
import useRewardGoldCoin from "hooks/useRewardGoldCoin";
import { useGameLaunch } from "../RedPacketRain";
import { ScrollEventName } from "pages/CoinCenter/constants";
import { ScrollAnalyticComp } from "@xmly/react-native-page-analytics";
import xmlog from "utilsV2/xmlog";

const defaultTouchIcon = 'https://imagev2.xmcdn.com/storages/c902-audiofreehighqps/77/0A/GAqh9sAL05RfAAAi6wOXmSNH.png';
const defaultTouchLottie = 'https://aod.cos.tx.xmcdn.com/storages/99ed-audiofreehighqps/AB/B8/GKwRIDoL1_svAACsCQOaC8gI.json';
export default function TouchTask() {
  const queryTouchTask = useSetAtom(writeTouchTaskAtom);
  const taskInfo = useAtomValue(touchTaskAtom);
  const countdown = useAtomValue(countdownAtom);
  const [touchTaskVisible, setTouchTaskVisible] = useAtom(touchTaskVisibleAtom);
  const clock = useCountDown();
  const styles = getStyles();
  const [scrolling, setScrolling] = useState(false);
  const animatedValue = useRef(new Animated.Value(0)).current;
  const finishScrollTimer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const momentumScrollStarted = useRef(false);
  const touchLottie = useLottieResource(taskInfo?.dynamicIcon??defaultTouchLottie);
  const touchIcon = taskInfo?.icon??defaultTouchIcon;
  const reward = useRewardGoldCoin();
  const rewardType = taskInfo?.type === DailyTaskType.RED_PACKET_RAIN ? RewardType.RED_PACKET_RAIN : RewardType.TREASURE_BOX;

  // 使用统一的红包雨钩子
  const { preloadResources, launchGame } = useGameLaunch();

  async function onPress() {
    xmlog.click(68277, 'TouchTask', { currPage: 'welfareCenter' });
    // treasureClickReport();
    if (taskInfo?.type === DailyTaskType.RED_PACKET_RAIN) {
      // 启动红包雨游戏
      await launchGame(taskInfo, AD_SOURCE.RED_PACKET_RAIN, async (score) => {
        // 游戏结束后的回调
        await queryTouchTask();
      });
      return;
    }
    const result = await watchAd({
      coins: 0,
      sourceName: AD_SOURCE.SUSPEND_TOUCH,
      rewardType
    });
    if (result.success) {
      const { coins } = await reward({
        rewardType,
        coins: 0,
        sourceName: AD_SOURCE.SUSPEND_TOUCH,
        fallbackReq: result.fallbackReq || FallbackReqType.NORMAL,
        ecpm: result.ecpm,
        encryptType: result.encryptType,
        adId: result.adId,
        adResponseId: result.adResponseId,
      });
      if (coins > 0) {
        queryTouchTask();
      }
    }
  }

  function close() {
    xmlog.click(68276, 'TouchTask', { currPage: 'welfareCenter' });
    setTouchTaskVisible(false);
  }

  useEffect(() => {
    queryTouchTask({ init: true });
  }, []);

  useEffect(() => {
    if (touchTaskVisible) {
      onShow();
    }
  }, [touchTaskVisible]);

  useEffect(() => {
    // 如果是红包雨任务，提前预加载资源
    if (taskInfo?.type === DailyTaskType.RED_PACKET_RAIN) {
      preloadResources();
    }
  }, [taskInfo?.type]);

  useEffect(() => {
    if (touchTaskVisible) {
      const onScrollBeginDragListener = GlobalEventEmitter.addListener(ScrollEventName.onScrollBeginDrag, () => {
        setScrolling(true);
      });
      const onScrollEndDragListener = GlobalEventEmitter.addListener(ScrollEventName.onScrollEndDrag, () => {
        setTimeout(() => {
          if (momentumScrollStarted.current) return;
          if (finishScrollTimer.current) clearTimeout(finishScrollTimer.current);
          finishScrollTimer.current = setTimeout(() => { setScrolling(false); }, 1000);
        }, 500);
      });
      const onMomentumScrollBegin = GlobalEventEmitter.addListener(ScrollEventName.onMomentumScrollBegin, () => {
        momentumScrollStarted.current = true;
      });
      const onMomentumScrollEnd = GlobalEventEmitter.addListener(ScrollEventName.onMomentumScrollEnd, () => {
        momentumScrollStarted.current = false;
        if (finishScrollTimer.current) clearTimeout(finishScrollTimer.current);
        finishScrollTimer.current = setTimeout(() => { setScrolling(false); }, 1000);
      });
      return () => {
        if (finishScrollTimer.current) clearTimeout(finishScrollTimer.current);
        onScrollBeginDragListener.remove();
        onScrollEndDragListener.remove();
        onMomentumScrollBegin.remove();
        onMomentumScrollEnd.remove();
      }
    }
    return () => { }
  }, [touchTaskVisible])

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: scrolling ? 1 : 0,
      duration: 300,
      useNativeDriver: true
    }).start();
  }, [scrolling, animatedValue])

  function onShow() {
    xmlog.event(68275, 'slipPage', { currPage: 'welfareCenter' });
  }

  return touchTaskVisible ? <Animated.View style={[styles.container, {
    opacity: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [1, .3],
    }),
    transform: [{
      translateX: animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [0, 40],
      })
    }]
  }]}>
    <Touch
      onPress={close}
      style={styles.closeBtn}
    >
      {/* <BetterImage
        source={{
          uri: 'https://imagev2.xmcdn.com/storages/b0d8-audiofreehighqps/4A/93/GKwRIW4L14jZAAADDQOZwW32.png',
        }}
        imgHeight={16}
        imgWidth={16}
        style={styles.closeIcon}
      /> */}
    </Touch>
    <Touch
      disabled={!!countdown && countdown > 0}
      onPress={onPress}
      style={styles.touch}
    >
      {countdown === 0 && touchLottie ? <AnimatedLottieView
        source={touchLottie}
        autoPlay
        loop
        style={styles.touchIcon} />
        :
        <BetterImage
          source={{
            uri: touchIcon,
          }}
          key={touchIcon}
          imgHeight={66}
          imgWidth={66}
          style={[styles.touchIcon]}
        />
      }
      <ConfirmButton
        text={clock ? `${clock}` : taskInfo?.title ?? '限时领金币'}
        onPress={onPress}
        colors={['#FF4444', '#FF8585']}
        style={styles.touchBtn}
        textStyle={styles.touchBtnText}
        disabled={!!countdown && countdown > 0}
      />
    </Touch>
  </Animated.View> : null
}