import { StyleSheet } from 'react-native'
import { px } from 'utils/px'
import { Dimensions } from 'react-native'

const CIRCLE_SIZE = 33
const LINE_HEIGHT = 4
const DOT_SIZE = 10
const { width } = Dimensions.get('window')

/**
 * 获取ShareCoins模块的样式对象
 * @param theme - 当前主题对象（包含颜色等配置）
 * @returns 返回StyleSheet对象，包含本模块所有用到的样式
 */
export const getStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      paddingTop: px(10)
      // padding: 20,
      // borderRadius: 16,
    },
    foldedTitle: {
      textAlign: 'left',
      fontSize: px(14),
      color: theme.collapseTitleColor
    },
    foldedHeaderTitle: {
      display: 'flex',
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    headerTitle: {
      paddingHorizontal: px(16),
      textAlign: 'center',
    },
    nextText: {
      color: theme.nextTextColor,
      fontSize: 11
    },
    titleBold: { 
      fontSize: 17,
      color: theme.titleColor, 
      fontWeight: 'bold', 
      textAlign: 'center',
    },
    titleHighlight: { 
      color: '#FF4444', 
      fontWeight: 'bold' 
    },
    subTitle: {
      marginTop: px(8),
      fontSize: 12,
      color: '#917f7f',
      textAlign: 'center',
    },
    collapseSubTitle: {
      marginTop: px(4),
      fontSize: px(12),
      color: theme.collapseSubTitleColor,
    },
    failedTitle: {
      color: theme.failedTitleColor,
      fontSize: 13,
      fontWeight: 'bold'
    },
    failedSubTitle: { 
      fontSize: 11, 
      color: theme.failedTitleColor, 
      marginTop: 8, 
      textAlign: 'center', 
    },
    subTitleHighlight: { color: '#FF4444', fontWeight: 'bold' },
    mask: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 1000,
      justifyContent: 'center',
      alignItems: 'center',
    },
    failedContainer: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 1000,
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: 16,
    },
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    itemContainer: {
      flex: 1,
    },
    item: {
      alignItems: 'center',
      height: px(39),

      // width: CIRCLE_SIZE,
    },
    circleChecked: {
      width: CIRCLE_SIZE,
      height: CIRCLE_SIZE,
      borderRadius: CIRCLE_SIZE / 2,
      backgroundColor: theme.circleCheckedBgColor,
      justifyContent: 'center',
      alignItems: 'center',
    },
    checkMark: {
      color: '#FF4444',
      fontSize: 28,
      fontWeight: 'bold',
    },
    circleCurrent: {
      width: CIRCLE_SIZE,
      height: CIRCLE_SIZE,
      borderRadius: CIRCLE_SIZE / 2,
      backgroundColor: '#FF4444',
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 4,
      borderColor: '#FF4444',
    },
    progressContainer: { 
      marginTop: 24, 
      position: 'relative',
      marginHorizontal: px(15),
      overflow: 'hidden'
    },
    progressRing: {
      width: CIRCLE_SIZE - 8,
      height: CIRCLE_SIZE - 8,
      borderRadius: (CIRCLE_SIZE - 8) / 2,
      backgroundColor: '#fff',
      justifyContent: 'center',
      alignItems: 'center',
    },
    progressText: { color: '#FF4444', fontWeight: 'bold', fontSize: 18 },
    circleUnfinished: {
      width: CIRCLE_SIZE,
      height: CIRCLE_SIZE,
      borderRadius: CIRCLE_SIZE / 2,
      backgroundColor: theme.circleBgColor,
      justifyContent: 'center',
      alignItems: 'center',
    },
    circleText: { 
      color: theme.circleTextColor, 
      fontSize: 19, 
      fontWeight: 'bold',
      opacity: theme.circleTextColorOpacity,
      marginLeft: px(1),
      marginBottom: px(1),
    },
    lineRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginTop: -10,
      marginBottom: 8,
      justifyContent: 'center',
    },
    bold: {
      color: '#F04747',
    },
    line: {
      height: LINE_HEIGHT,
      width: 24,
      marginHorizontal: 0,
      borderRadius: 2,
    },
    lineActive: { backgroundColor: '#FF4444' },
    lineInactive: { backgroundColor: '#F7F8FA' },
    stepDot: {
      width: px(6),
      height: px(6),
      borderRadius: px(3),
      borderWidth: px(1),
      borderColor: theme.stepDotBorderColor,
      backgroundColor: 'rgba(255, 68, 68, 1)',
      alignSelf: 'center',
      position: 'absolute',
      zIndex: 1000,
    },
    stepDotUnfinished: {
      backgroundColor: theme.unfinishedDot,
    },
    currentDot: {
      backgroundColor: theme.currentDotColor || '#FF4B4B',
      width: 12,
      height: 12,
      borderRadius: 6,
    },
    completedDot: {
      backgroundColor: theme.completedDotColor || '#FFB6B6',
    },
    label: { marginTop: 8, fontSize: 16 },
    labelActive: { color: '#BFBFBF' },
    labelCurrent: { color: '#FF4444', fontWeight: 'bold' },
    labelInactive: { color: '#BFBFBF' },
    adButton: {
      marginTop: px(32),
      backgroundColor: '#FF4444',
      borderRadius: 32,
      paddingVertical: px(16),
      alignItems: 'center',
    },
    adButtonText: {
      color: '#fff',
      fontSize: 18,
      fontWeight: 'bold',
    },
    stepProgress: {
      width: '100%',
      height: px(6),
      display: 'flex',
      position: 'relative',
    },
    stepLine: {
      width: '101%',
      height: px(2),
      position: 'absolute',
      top: '50%',
      marginTop: -px(1),
      // backgroundColor: theme.dotColor || '#E5E5E5',
      // backgroundColor: theme.dotColor || '#E5E5E5',
    },
    stepTime: {
      fontSize: 12,
      color: '#999999',
      marginBottom: px(8),
    },
    stepInfo: {
      alignItems: 'center',
      marginTop: px(8),
    },
    adBtnRow: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      marginTop: px(22),
      marginBottom: px(6),
      paddingHorizontal: px(16),
      position: 'relative'
      // backgroundColor: 'red'
      // paddingHorizontal: 8,
    },
    adBtn: {
      flex: 1,
      height: px(38),
      borderRadius: px(19),
      maxWidth: px(227),
      justifyContent: 'center',
      alignItems: 'center',
      flexDirection: 'row',
      fontSize: 14
    },
    adBtnLeft: {
      backgroundColor: theme.btnLeftBgColor,
      marginRight: px(15),
    },
    adBtnRight: {
      backgroundColor: '#FF4444',
    },
    adBtnTextLeft: {
      color: theme.btnLeftColor,
      fontWeight: 'bold',
      fontSize: px(13),
      fontFamily: 'XmlyNumber',
    },
    adBtnTextRight: {
      color: '#fff',
      fontWeight: 'bold',
      fontSize: px(13),
      fontFamily: 'XmlyNumber',
    },
    adBtnCountLeft: {
      color: '#888',
      fontWeight: '400',
      marginLeft: px(4),
    },
    adBtnCountRight: {
      color: '#fff',
      fontWeight: '400',
      marginLeft: px(4),
    },
    fireworksContainer: {
      position: 'relative',
    },
  })
