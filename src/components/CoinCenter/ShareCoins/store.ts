import { atom } from 'jotai';
import { 
  queryCarveUpAward, 
  ShareCoinsTaskDayStatus, 
  ShareCoinsTaskStatus, 
  ShareCoinsModalInfo,
  AwardStatus,
} from 'services/welfare/shareCoinsTask';

export interface DayType { 
  label: string, 
  status: ShareCoinsTaskDayStatus, 
  text: string,
  totalTimes?: number,
  progress?: number,
}

export interface ShareCoinsTaskInfo {
  success: boolean,
  failCode: number,
  taskStatus: ShareCoinsTaskStatus,
  dayInfo: DayType[],
  buttonText: string,
  minButtonText: string,
  enableReward: boolean,
  totalTimes: number,
  alreadyTimes: number,
  replenishSignInBtnText: string,
  replenishSignInMinBtnText: string,
  replenishSignInTotalTimes: number,
  replenishSignInAlreadyTimes: number,
  subTitle: string,
  shareCoinsModalInfo?: ShareCoinsModalInfo,
}

// 初始状态
const initialShareCoinsTaskInfo: ShareCoinsTaskInfo = {
  success: false,
  failCode: 200,
  taskStatus: 1,
  dayInfo: [
    { label: '周一', status: ShareCoinsTaskDayStatus.UNFINISHED, text: '看' },
    { label: '周二', status: ShareCoinsTaskDayStatus.UNFINISHED, text: '广' },
    { label: '周三', status: ShareCoinsTaskDayStatus.UNFINISHED, text: '告' },
    { label: '周四', status: ShareCoinsTaskDayStatus.UNFINISHED, text: '瓜' },
    { label: '周五', status: ShareCoinsTaskDayStatus.UNFINISHED, text: '分' },
    { label: '周六', status: ShareCoinsTaskDayStatus.UNFINISHED, text: '大' },
    { label: '周日', status: ShareCoinsTaskDayStatus.UNFINISHED, text: '奖' }
  ],
  buttonText: '',
  minButtonText: '',
  enableReward: true,
  totalTimes: 0,
  alreadyTimes: 0,
  replenishSignInBtnText: '',
  replenishSignInMinBtnText: '',
  replenishSignInTotalTimes: 0,
  replenishSignInAlreadyTimes: 0,
  subTitle: '有1.5万人正在挑战，预估人均可领{{5000}}金币',
  shareCoinsModalInfo: undefined
};

const data = {
  "success": true,         // 是否成功
  "failCode":200,            //失败code，成功为200
  "taskStatus": 1, // 1 -- 任务进行中， 2--任务失败 3--任务已完成
  "buttonText": "看激励视频，领现金（1/4）", // 按钮文案
  "minButtonText": "看广告",
  "enableReward": true, // 是否可以领取
  "alreadyRewardTimes": 3,
  "maxRewardTimes": 4,
  "progress": 5, // 任务进度 0-7, 
  "dayOfWeek": 6, // 当前是周几
  // 副标题，高亮{{}}这个符号包裹的文案
  "subTitle": "有1.5万人正在挑战，预估人均可领{{5000}}金币",
  "replenishSignIn": { //补签
      "replenishSignInDays": 4, // 补签的第N天
      "totalTimes": 2, // 需要看广告总数
      "alreadyTimes": 0, // 已经看的数量
      "btnText": "看视频补签（0/2）", // 按钮文案
     "minBtnText": "去补签"
  }
}

const testShareCoinsModalData: ShareCoinsModalInfo = {
  shareCoinsModalTitle: '瓜分百亿金币',
    shareCoinsModalSubTitle: '测试测试测试',
    dailyCoinsAwardDetail: [
      {
        stepNo: 0,
        amount: '10000',
        amountText: '1万',
        status: AwardStatus.GOT,
        bigAmount: false,
        today: false,
      },
      {
        stepNo: 1,
        amount: '20000',
        amountText: '2万',
        status: AwardStatus.GOT,
        bigAmount: false,
        today: false,
      },
      {
        stepNo: 2,
        amount: '30000',
        amountText: '3万',
        status: AwardStatus.CAN_GET,
        bigAmount: true,
        today: true,
      },
      {
        stepNo: 3,
        amount: '40000',
        amountText: '4万',
        status: AwardStatus.CAN_GET,
        bigAmount: false,
        today: false,
      },
      {
        stepNo: 4,
        amount: '50000',
        amountText: '5万',
        status: AwardStatus.CAN_GET,
        bigAmount: false,
        today: false,
      },
      {
        stepNo: 5,
        amount: '60000',
        amountText: '6万',
        status: AwardStatus.CAN_GET,
        bigAmount: false,
        today: false,
      },
      {
        stepNo: 6,
        amount: '80000',
        amountText: '8万',
        status: AwardStatus.CAN_GET,
        bigAmount: true,
        today: false,
      }   
    ]
}

// 创建状态原子
export const shareConinsTaskAtom = atom<ShareCoinsTaskInfo>(initialShareCoinsTaskInfo);

// 写入/更新任务状态的原子
export const writeShareConinsTaskAtom = atom(
  null,
  async (get, set) => {
    const task = get(shareConinsTaskAtom)
    try {
      const response = await queryCarveUpAward();
      console.log('-----queryCarveUpAward----- response ---->>>>>>>>---->', response)
      // const response = { data };
      
      if(!response?.data?.success) {
        set(shareConinsTaskAtom, {...task, success: false});
      } else {
        const newInfo = {...task}
        let dayInfo = newInfo.dayInfo
        console.log('------ dayInfo --------->', dayInfo)
        dayInfo = dayInfo.map((item, index) => {
          if(index < response?.data?.progress) {
            return {
              ...item,
              status: ShareCoinsTaskDayStatus.DONE,
            }
          } 
          if(index === response?.data?.dayOfWeek - 1) {
            return {
              ...item,
              status: ShareCoinsTaskDayStatus.PENDING,
              totalTimes: response?.data?.maxRewardTimes,
              progress: response?.data?.alreadyRewardTimes,
            }
          }
          
          return item
        })
        if(response?.data?.replenishSignIn) {
          dayInfo[response?.data?.replenishSignIn?.replenishSignInDays - 1] = {
            ...dayInfo[response?.data?.replenishSignIn?.replenishSignInDays - 1],
            status: ShareCoinsTaskDayStatus.COMPLEMENT,
            totalTimes: response?.data?.replenishSignIn?.totalTimes,
            progress: response?.data?.replenishSignIn?.alreadyTimes,
          }
          newInfo.replenishSignInBtnText = response?.data?.replenishSignIn?.btnText
          newInfo.replenishSignInMinBtnText = response?.data?.replenishSignIn?.minBtnText
          newInfo.replenishSignInTotalTimes = response?.data?.replenishSignIn?.totalTimes
          newInfo.replenishSignInAlreadyTimes = response?.data?.replenishSignIn?.alreadyTimes
        } else {
          newInfo.replenishSignInBtnText = ''
          newInfo.replenishSignInMinBtnText = ''
          newInfo.replenishSignInTotalTimes = 0
          newInfo.replenishSignInAlreadyTimes = 0
        }
        newInfo.buttonText = response?.data?.buttonText
        newInfo.minButtonText = response?.data?.minButtonText
        newInfo.enableReward = response?.data?.enableReward
        newInfo.taskStatus = response?.data?.taskStatus
        newInfo.alreadyTimes = response?.data?.alreadyRewardTimes
        newInfo.totalTimes = response?.data?.maxRewardTimes
        newInfo.success = response?.data?.success
        // newInfo.taskStatus = 1
        newInfo.subTitle = response?.data?.subTitle
        set(shareConinsTaskAtom, {...task, ...newInfo, dayInfo, shareCoinsModalInfo: response?.data?.shareCoinsModalInfo})
      }
      
    } catch (error) {
      set(shareConinsTaskAtom, {...task, success: true})
      console.error('Failed to update listen task:', error);
    }
  }
);
