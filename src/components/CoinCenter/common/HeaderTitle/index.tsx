import React from "react";
import { View, Text } from "react-native";
import { BetterImage } from "@xmly/rn-components";
import { getStyles } from "./styles";
import { useAtomValue } from "jotai";
import headerTitleThemeAtom from "./theme";

interface HeaderTitleProps {
  title: string;
  titleIcon: string;
  children?: React.ReactNode;
  titleChildren?: React.ReactNode;
}

export default function HeaderTitle({ title, titleIcon, children, titleChildren }: HeaderTitleProps) {
  const theme = useAtomValue(headerTitleThemeAtom);
  const styles = getStyles(theme);

  return (
    <View style={styles.header}>
      <View style={styles.titleContainer}>
        {/* <BetterImage
          source={{ uri: titleIcon }}
          style={styles.titleIcon}
          imgHeight={16}
          imgWidth={16}
        /> */}
        <Text style={styles.title}>{title}</Text>
        {titleChildren}
      </View>
      {children}
    </View>
  );
} 