import { atom } from 'jotai';
import { themeAtom } from '../../../../atom/theme';

export const darkTheme = {
  backgroundColor: 'transparent',
  borderColor: 'rgba(255, 255, 255, 0.1)',
  titleColor: '#FFFFFF',
  descColor: '#DCDCDC',
  worthColor: '#FF6B35',
  buttonBgColor: '#FF4444',
  disabledButtonBgColor: '#666666',
};

export const lightTheme = {
  backgroundColor: 'transparent',
  borderColor: 'rgba(0, 0, 0, 0.1)',
  titleColor: '#111111',
  descColor: '#666666',
  worthColor: '#FF6B35',
  buttonBgColor: '#FF4444',
  disabledButtonBgColor: '#CCCCCC',
};

const exchangeTaskItemThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
});

export default exchangeTaskItemThemeAtom;
