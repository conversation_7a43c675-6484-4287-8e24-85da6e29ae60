import { atom } from 'jotai';
import { themeAtom } from '../../../../atom/theme';

export const darkTheme = {
  taskNameColor: '#FFFFFF',
  taskRewardColor: '#8D8D91',
  taskWorthColor: '#FF6B35',
  buttonBgColor: '#FF4B4B',
  buttonTextColor: '#FFFFFF',
  countdownColor: 'rgba(255, 255, 255, 0.3)',
}

export const lightTheme = {
  taskNameColor: '#111111',
  taskRewardColor: '#999999',
  taskWorthColor: '#FF6B35',
  buttonBgColor: '#FF4B4B',
  buttonTextColor: '#FFFFFF',
  countdownColor: 'rgba(44, 44, 60, 0.3)',
}

const exchangeTaskItemThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default exchangeTaskItemThemeAtom;
