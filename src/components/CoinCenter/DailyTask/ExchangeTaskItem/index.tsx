import React, { useState, useRef } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
// 暂时注释掉可能有问题的导入，使用简化版本
// import { useAtomValue } from 'jotai';
// import { TaskItemType, TaskStatus } from '../../../../typesV2/taskList';
// import { getStyles } from './styles';
// import exchangeTaskItemThemeAtom from './theme';
// import TaskButton from '../../common/TaskButton';
// import performTask from '../../../../modulesV2/performTask';
// import { taskListAid } from '../../../../constantsV2';
// import xmlog from '../../../../utilsV2/xmlog';
// import { Toast } from '@xmly/rn-sdk';
// //@ts-ignore
// import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics';

interface ExchangeTaskItemProps {
  item: any; // 简化类型定义
  isFirst?: boolean;
  loading?: boolean;
  index: number;
}

export default function ExchangeTaskItem({
  item,
  isFirst,
  loading,
  index,
}: ExchangeTaskItemProps) {
  // 简化版本，暂时使用内联样式
  const [isProcessing, setIsProcessing] = useState(false);
  const clickBtnEnable = useRef<boolean>(true);

  async function handlePress() {
    console.log('换量任务点击:', item.title);
    // 简化版本的点击处理
    if (isProcessing || !clickBtnEnable.current) return;

    try {
      setIsProcessing(true);
      clickBtnEnable.current = false;

      setTimeout(() => {
        clickBtnEnable.current = true;
        setIsProcessing(false);
      }, 1000);

      // 这里可以添加实际的任务执行逻辑
      // await performTask(item, taskListAid);
    } catch (error) {
      console.error('执行换量任务失败:', error);
      // Toast.info('执行任务失败～');
    }
  }

  function getButtonText() {
    if (item.status === 'finished') {
      return '去领取';
    }
    return item.statusText || '去完成';
  }

  function renderButton() {
    const buttonDisabled = isProcessing || loading;
    const buttonText = getButtonText();

    return (
      <TouchableOpacity
        style={{
          minWidth: 80,
          height: 32,
          borderRadius: 16,
          backgroundColor: buttonDisabled ? '#CCCCCC' : '#FF4444',
          justifyContent: 'center',
          alignItems: 'center',
        }}
        disabled={buttonDisabled}
        onPress={handlePress}
      >
        <Text style={{ color: 'white', fontSize: 14 }}>
          {buttonText}
        </Text>
      </TouchableOpacity>
    );
  }

  return (
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: 16,
      paddingHorizontal: 16,
      borderBottomWidth: isFirst ? 0 : 1,
      borderBottomColor: '#f0f0f0',
      backgroundColor: 'transparent',
    }}>
      <View style={{ flex: 1, marginRight: 12 }}>
        <Text style={{ fontSize: 16, fontWeight: '500', color: '#333', marginBottom: 4 }}>
          {item.title}
        </Text>
        <Text style={{ fontSize: 14, color: '#666', marginBottom: 4 }}>
          {item.desc}
        </Text>
        {item.worth ? (
          <Text style={{ fontSize: 12, color: '#FF6B35', fontWeight: '500' }}>
            +{item.worth}积分
          </Text>
        ) : null}
      </View>
      {renderButton()}
    </View>
  );
}
