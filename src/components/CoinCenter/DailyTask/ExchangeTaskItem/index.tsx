import React, { useState, useRef, useEffect } from 'react';
import { View, Text } from 'react-native';
import { useAtomValue, useSetAtom } from 'jotai';
import { TaskItemType, TaskStatus } from '../../../../typesV2/taskList';
import { getStyles } from './styles';
import exchangeTaskItemThemeAtom from './theme';
import TaskButton from '../../common/TaskButton';
import performTask from '../../../../modulesV2/performTask';
import { taskListAid } from '../../../../constantsV2';
import xmlog from '../../../../utilsV2/xmlog';
import { Toast } from '@xmly/rn-sdk';
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics';
import { updateDailyTaskAtom } from '../atom';

interface ExchangeTaskItemProps {
  item: TaskItemType;
  isFirst?: boolean;
  loading?: boolean;
  index: number;
}

export default function ExchangeTaskItem({
  item,
  isFirst,
  loading,
  index,
}: ExchangeTaskItemProps) {
  const theme = useAtomValue(exchangeTaskItemThemeAtom);
  const styles = getStyles(theme);
  const updateDailyTask = useSetAtom(updateDailyTaskAtom);
  const [isProcessing, setIsProcessing] = useState(false);
  const [btnText, setBtnText] = useState('');
  const clickBtnEnable = useRef<boolean>(true);

  useEffect(() => {
    // 根据任务状态设置按钮文本
    if (item.status === TaskStatus.finished) {
      setBtnText('去领取');
    } else if (item.status === TaskStatus.received) {
      setBtnText('已完成');
    } else {
      setBtnText(item.statusText || '去完成');
    }
  }, [item.status, item.statusText]);

  async function handlePress() {
    clickReport();
    if (isProcessing || !clickBtnEnable.current) return;

    try {
      setIsProcessing(true);
      clickBtnEnable.current = false;

      // 使用积分中心的任务执行逻辑
      await performTask(item, taskListAid);

      // 更新任务列表
      updateDailyTask();

      setTimeout(() => {
        clickBtnEnable.current = true;
        setIsProcessing(false);
      }, 1000);

    } catch (error) {
      console.error('执行换量任务失败:', error);
      Toast.info('执行任务失败～');
      setIsProcessing(false);
      clickBtnEnable.current = true;
    }
  }

  function onShow() {
    // 福利中心-每日任务  控件曝光
    xmlog.event(67696, 'slipPage', {
      currPage: 'welfareCenter',
      moduleTitle: '每日任务',
      taskTitle: item.title,
      taskId: `${item.id}`,
      positionNew: `${index + 1}`,
      Item: btnText
    });
  }

  function clickReport() {
    // 福利中心-任务-任务条  点击事件
    xmlog.click(67695, 'ExchangeTaskItem', {
      currPage: 'welfareCenter',
      moduleTitle: '每日任务',
      taskTitle: item.title,
      taskId: `${item.id}`,
      positionNew: `${index + 1}`,
      Item: btnText
    });
  }

  function renderButton() {
    const buttonDisabled = isProcessing || loading || item.status === TaskStatus.received;

    return (
      <TaskButton
        text={btnText}
        disabled={buttonDisabled}
        onPress={handlePress}
        loading={isProcessing}
      />
    );
  }

  return (
    <ScrollAnalyticComp
      itemKey={`ExchangeTaskItem_${item.id}_${item.title}`}
      onShow={onShow}
    >
      <View style={[styles.taskItem, isFirst && styles.firstTaskItem]}>
        <View style={styles.taskInfo}>
          <Text style={styles.taskName}>{item.title}</Text>
          <Text style={styles.taskReward}>{item.desc}</Text>
          {item.worth ? (
            <Text style={styles.taskWorth}>+{item.worth}积分</Text>
          ) : null}
        </View>
        {renderButton()}
      </View>
    </ScrollAnalyticComp>
  );
}
