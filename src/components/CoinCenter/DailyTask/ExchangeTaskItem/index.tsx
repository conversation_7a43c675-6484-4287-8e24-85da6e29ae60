import React, { useState, useRef, useEffect } from 'react';
import { View, Text } from 'react-native';
import { useAtomValue, useSetAtom } from 'jotai';
import { TaskItemType, TaskStatus } from '../../../../typesV2/taskList';
import { getStyles } from './styles';
import exchangeTaskItemThemeAtom from './theme';
import TaskButton from '../../common/TaskButton';
import performTask from '../../../../modulesV2/performTask';
import { taskListAid } from '../../../../constantsV2';
import xmlog from '../../../../utilsV2/xmlog';
import { Toast } from '@xmly/rn-sdk';
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics';
import { updateDailyTaskAtom } from '../atom';
import useRewardGoldCoin from '../../../../hooks/useRewardGoldCoin';
import { AD_SOURCE, RewardType, FallbackReqType } from '../../../../constants/ad';
import watchAd from '../../../../utils/watchAd';

interface ExchangeTaskItemProps {
  item: TaskItemType;
  isFirst?: boolean;
  loading?: boolean;
  index: number;
}

export default function ExchangeTaskItem({
  item,
  isFirst,
  loading,
  index,
}: ExchangeTaskItemProps) {
  const theme = useAtomValue(exchangeTaskItemThemeAtom);
  const styles = getStyles(theme);
  const updateDailyTask = useSetAtom(updateDailyTaskAtom);
  const [isProcessing, setIsProcessing] = useState(false);
  const [btnText, setBtnText] = useState('');
  const clickBtnEnable = useRef<boolean>(true);
  const rewardGoldCoin = useRewardGoldCoin();

  useEffect(() => {
    // 根据任务状态设置按钮文本，统一使用"去完成"
    if (item.status === TaskStatus.received) {
      setBtnText('已完成');
    } else {
      setBtnText('去完成');
    }
  }, [item.status]);

  async function handlePress() {
    clickReport();
    if (isProcessing || !clickBtnEnable.current || item.status === TaskStatus.received) return;

    try {
      setIsProcessing(true);
      clickBtnEnable.current = false;

      // 先执行原有的任务逻辑（跳转到其他APP）
      await performTask(item, taskListAid);

      // 任务完成后，调用金币奖励接口
      // 模拟看广告的流程，但实际是完成换量任务
      const mockAdResult = {
        success: true,
        adId: 0,
        adResponseId: 0,
        ecpm: '',
        encryptType: '',
        fallbackReq: FallbackReqType.NORMAL
      };

      // 调用金币奖励接口，参考逛商城领金币的参数
      const result = await rewardGoldCoin({
        rewardType: RewardType.EXCHANGE_TASK,
        coins: 100, // 默认100金币，与逛商城任务一致
        sourceName: AD_SOURCE.EXCHANGE_TASK,
        adId: mockAdResult.adId,
        adResponseId: mockAdResult.adResponseId,
        ecpm: mockAdResult.ecpm,
        encryptType: mockAdResult.encryptType,
        fallbackReq: mockAdResult.fallbackReq,
        extMap: JSON.stringify({
          taskId: item.id,
          taskTitle: item.title,
          originalTaskType: item.taskType
        })
      });

      if (result?.success) {
        // 更新任务列表
        updateDailyTask();
      }

      setTimeout(() => {
        clickBtnEnable.current = true;
        setIsProcessing(false);
      }, 1000);

    } catch (error) {
      console.error('执行换量任务失败:', error);
      Toast.info('执行任务失败～');
      setIsProcessing(false);
      clickBtnEnable.current = true;
    }
  }

  function onShow() {
    // 福利中心-每日任务  控件曝光
    xmlog.event(67696, 'slipPage', {
      currPage: 'welfareCenter',
      moduleTitle: '每日任务',
      taskTitle: item.title,
      taskId: `${item.id}`,
      positionNew: `${index + 1}`,
      Item: btnText
    });
  }

  function clickReport() {
    // 福利中心-任务-任务条  点击事件
    xmlog.click(67695, 'ExchangeTaskItem', {
      currPage: 'welfareCenter',
      moduleTitle: '每日任务',
      taskTitle: item.title,
      taskId: `${item.id}`,
      positionNew: `${index + 1}`,
      Item: btnText
    });
  }

  function renderButton() {
    const buttonDisabled = isProcessing || loading || item.status === TaskStatus.received;

    return (
      <TaskButton
        text={btnText}
        disabled={buttonDisabled}
        onPress={handlePress}
        loading={isProcessing}
      />
    );
  }

  return (
    <ScrollAnalyticComp
      itemKey={`ExchangeTaskItem_${item.id}_${item.title}`}
      onShow={onShow}
    >
      <View style={[styles.taskItem, isFirst && styles.firstTaskItem]}>
        <View style={styles.taskInfo}>
          <Text style={styles.taskName}>{item.title}</Text>
          <Text style={styles.taskReward}>清动浏览商品30秒，立得100金币</Text>
          <Text style={styles.taskWorth}>+100金币</Text>
        </View>
        {renderButton()}
      </View>
    </ScrollAnalyticComp>
  );
}
