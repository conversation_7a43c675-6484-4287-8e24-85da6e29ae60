import { StyleSheet, Platform } from 'react-native';
import { px } from '../../../../utils/px';
import { darkTheme } from './theme';

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  taskItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: px(17),
  },
  firstTaskItem: {
    marginTop: px(12),
  },
  taskInfo: {
    flex: 1,
  },
  taskName: {
    fontSize: px(14),
    color: theme.taskNameColor,
    fontWeight: Platform.select({ ios: '500', android: 'bold' }),
  },
  taskReward: {
    fontSize: px(12),
    color: theme.taskRewardColor,
    marginTop: px(4),
    fontFamily: 'XmlyNumber',
  },
  taskWorth: {
    fontSize: px(12),
    color: theme.taskWorthColor,
    marginTop: px(2),
    fontWeight: '500',
  },
});
