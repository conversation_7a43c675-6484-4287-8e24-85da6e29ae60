import { StyleSheet } from 'react-native';
import { px } from '../../../../utils/px';
import { darkTheme } from './theme';

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  taskItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: px(16),
    paddingHorizontal: px(16),
    borderBottomWidth: px(1),
    borderBottomColor: theme.borderColor,
    backgroundColor: theme.backgroundColor,
  },
  firstTaskItem: {
    borderTopWidth: 0,
  },
  taskInfo: {
    flex: 1,
    marginRight: px(12),
  },
  taskName: {
    fontSize: px(16),
    fontWeight: '500',
    color: theme.titleColor,
    marginBottom: px(4),
  },
  taskReward: {
    fontSize: px(14),
    color: theme.descColor,
    marginBottom: px(4),
  },
  taskWorth: {
    fontSize: px(12),
    color: theme.worthColor,
    fontWeight: '500',
  },
  taskButton: {
    minWidth: px(80),
    height: px(32),
    borderRadius: px(16),
    backgroundColor: theme.buttonBgColor,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingButton: {
    backgroundColor: theme.disabledButtonBgColor,
  },
});
