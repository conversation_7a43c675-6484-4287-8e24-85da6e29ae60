import React, { useEffect, useMemo } from "react";
import { View } from "react-native";
import { getStyles } from "./styles";
import ModuleCard from "../common/ModuleCard";
import HeaderTitle from "../common/HeaderTitle";
import { useAtomValue, useSetAtom } from "jotai";
// 暂时注释掉可能有问题的导入
// import { useSelector } from "react-redux";
import dailyTaskThemeAtom from "./theme";
import { dailyTaskAtom, updateDailyTaskAtom } from "./atom";
import DailyTaskItem from "./DailyTaskItem";
import ExchangeTaskItem from "./ExchangeTaskItem";
// import { RootState, store } from "../../../store";
// import { TaskItemType, TaskItemKind } from "../../../typesV2/taskList";
// import { taskListAid } from "../../../constantsV2";

export default function DailyTask() {
  const styles = getStyles();
  const theme = useAtomValue(dailyTaskThemeAtom);
  const { list, countdowns, loading, updatingPositions } = useAtomValue(dailyTaskAtom);
  const updateDailyTask = useSetAtom(updateDailyTaskAtom);

  // 暂时使用模拟数据进行测试
  const mockExchangeTasks = useMemo(() => [
    {
      id: 1001,
      title: '去中国移动领话费流量',
      desc: '移动用户专享福利',
      worth: 50,
      status: 'unfinished'
    },
    {
      id: 1002,
      title: '去快手看短视频',
      desc: '每天都能领金币',
      worth: 50,
      status: 'unfinished'
    },
    {
      id: 1003,
      title: '去支付宝去领水果',
      desc: '种果树的水果，助力贵州增收',
      worth: 50,
      status: 'finished'
    }
  ], []);

  useEffect(() => {
    updateDailyTask();
    // 这里可以添加获取真实换量任务的逻辑
    console.log('DailyTask mounted, mock exchange tasks:', mockExchangeTasks);
  }, []);

  return (
    <ModuleCard style={styles.container}>
      <HeaderTitle title="每日任务" titleIcon={theme.titleIcon} />
      <View style={styles.taskList}>
        {/* 原有的每日任务 */}
        {list.map((item, index) => {
          const key = `${item.positionId}${item.title}`;
          return (
            <DailyTaskItem
              index={index}
              key={key}
              item={item}
              countdown={countdowns[key] || 0}
              isFirst={index === 0 && mockExchangeTasks.length === 0}
              loading={loading}
              updatingPositions={updatingPositions}
            />
          )
        })}

        {/* 新增的换量任务 */}
        {mockExchangeTasks.map((item, index) => {
          const key = `exchange_${item.id}`;
          const isFirst = index === 0 && list.length === 0;
          return (
            <ExchangeTaskItem
              index={list.length + index}
              key={key}
              item={item}
              isFirst={isFirst}
              loading={loading}
            />
          )
        })}
      </View>
    </ModuleCard>
  );
}