import React, { useEffect } from "react";
import { View } from "react-native";
import { getStyles } from "./styles";
import ModuleCard from "../common/ModuleCard";
import HeaderTitle from "../common/HeaderTitle";
import { useAtomValue, useSetAtom } from "jotai";
import dailyTaskThemeAtom from "./theme";
import { dailyTaskAtom, updateDailyTaskAtom } from "./atom";
import DailyTaskItem from "./DailyTaskItem";
import ExchangeTaskItem from "./ExchangeTaskItem";
import { store } from "../../../store";
import { taskListAid } from "../../../constantsV2";
import { connect } from "react-redux";
import { RootState } from "../../../store";

interface DailyTaskProps {
  taskCenterTaskList: any[];
}

function DailyTask({ taskCenterTaskList }: DailyTaskProps) {
  const styles = getStyles();
  const theme = useAtomValue(dailyTaskThemeAtom);
  const { list, exchangeTasks, countdowns, loading, updatingPositions } = useAtomValue(dailyTaskAtom);
  const updateDailyTask = useSetAtom(updateDailyTaskAtom);

  useEffect(() => {
    const loadData = async () => {
      // 先确保积分中心的任务数据已加载
      await store.dispatch.taskCenter.getTaskList({ aid: taskListAid });

      // 然后加载每日任务数据（包括换量任务）
      updateDailyTask();
    };

    loadData();
  }, []);

  // 当积分中心任务列表更新时，重新获取换量任务
  useEffect(() => {
    if (taskCenterTaskList.length > 0) {
      console.log('积分中心任务列表已更新，重新获取换量任务');
      updateDailyTask();
    }
  }, [taskCenterTaskList]);

  return (
    <ModuleCard style={styles.container}>
      <HeaderTitle title="每日任务" titleIcon={theme.titleIcon} />
      <View style={styles.taskList}>
        {/* 原有的每日任务 */}
        {list.map((item, index) => {
          const key = `${item.positionId}${item.title}`;
          return (
            <DailyTaskItem
              index={index}
              key={key}
              item={item}
              countdown={countdowns[key] || 0}
              isFirst={index === 0 && exchangeTasks.length === 0}
              loading={loading}
              updatingPositions={updatingPositions}
            />
          )
        })}

        {/* 换量任务 */}
        {exchangeTasks.map((item, index) => {
          const key = `exchange_${item.id}`;
          const isFirst = index === 0 && list.length === 0;
          return (
            <ExchangeTaskItem
              index={list.length + index}
              key={key}
              item={item}
              isFirst={isFirst}
              loading={loading}
            />
          )
        })}
      </View>
    </ModuleCard>
  );
}

// 连接Redux状态
const mapStateToProps = (state: RootState) => ({
  taskCenterTaskList: state.taskCenter.taskList,
});

export default connect(mapStateToProps)(DailyTask);