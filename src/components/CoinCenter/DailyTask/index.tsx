import React, { useEffect } from "react";
import { View } from "react-native";
import { getStyles } from "./styles";
import ModuleCard from "../common/ModuleCard";
import HeaderTitle from "../common/HeaderTitle";
import { useAtomValue, useSetAtom } from "jotai";
import dailyTaskThemeAtom from "./theme";
import { dailyTaskAtom, updateDailyTaskAtom } from "./atom";
import DailyTaskItem from "./DailyTaskItem";
import ExchangeTaskItem from "./ExchangeTaskItem";
import { store } from "../../../store";
import { taskListAid } from "../../../constantsV2";

export default function DailyTask() {
  const styles = getStyles();
  const theme = useAtomValue(dailyTaskThemeAtom);
  const { list, exchangeTasks, countdowns, loading, updatingPositions } = useAtomValue(dailyTaskAtom);
  const updateDailyTask = useSetAtom(updateDailyTaskAtom);

  useEffect(() => {
    // 确保积分中心的任务数据已加载
    store.dispatch.taskCenter.getTaskList({ aid: taskListAid });

    // 加载每日任务数据
    updateDailyTask();

    console.log('DailyTask mounted, loading exchange tasks from task center');
  }, []);

  return (
    <ModuleCard style={styles.container}>
      <HeaderTitle title="每日任务" titleIcon={theme.titleIcon} />
      <View style={styles.taskList}>
        {/* 原有的每日任务 */}
        {list.map((item, index) => {
          const key = `${item.positionId}${item.title}`;
          return (
            <DailyTaskItem
              index={index}
              key={key}
              item={item}
              countdown={countdowns[key] || 0}
              isFirst={index === 0 && exchangeTasks.length === 0}
              loading={loading}
              updatingPositions={updatingPositions}
            />
          )
        })}

        {/* 换量任务 */}
        {exchangeTasks.map((item, index) => {
          const key = `exchange_${item.id}`;
          const isFirst = index === 0 && list.length === 0;
          return (
            <ExchangeTaskItem
              index={list.length + index}
              key={key}
              item={item}
              isFirst={isFirst}
              loading={loading}
            />
          )
        })}
      </View>
    </ModuleCard>
  );
}