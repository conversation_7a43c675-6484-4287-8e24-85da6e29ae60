import { atom } from 'jotai';
import { DailyTaskItem, queryDailyTask, DailyTaskType } from 'services/welfare';
import { TaskItemType } from '../../../typesV2/taskList';
import { store } from '../../../store';

interface DailyTaskState {
  loading: boolean;
  list: DailyTaskItem[];
  exchangeTasks: TaskItemType[]; // 换量任务列表
  countdowns: Record<string, number>; // 使用 positionId+title 作为 key
  updatingPositions: string[]; // 记录正在更新的任务ID
}

const initialState: DailyTaskState = {
  loading: false,
  list: [],
  exchangeTasks: [],
  countdowns: {},
  updatingPositions: [],
};

export const dailyTaskAtom = atom(initialState);

export const updateDailyTaskAtom = atom(
  null,
  async (get, set, key?: string) => {
    const state = get(dailyTaskAtom);
    if (state.loading) return;

    // 如果是特定任务触发的更新，先标记该任务为更新中
    if (key) {
      set(dailyTaskAtom, {
        ...state,
        updatingPositions: [...state.updatingPositions, key],
      });
    } else {
      set(dailyTaskAtom, { ...state, loading: true });
    }

    try {
      // 获取每日任务数据
      const dailyTaskResult = await queryDailyTask();

      // 获取换量任务数据（从积分中心的任务列表中筛选）
      const taskCenterState = store.getState().taskCenter;
      const exchangeTasks = taskCenterState.taskList.filter(task =>
        task.taskType === 12 || // 第三方任务
        (task.guideLink && task.guideLink.includes('://')) // 包含跳转链接的任务
      );

      const newCountdowns: Record<string, number> = {};
      let dailyTaskList: DailyTaskItem[] = [];

      if (dailyTaskResult.success && dailyTaskResult.list) {
        dailyTaskList = [...dailyTaskResult.list];

        dailyTaskResult.list.forEach(item => {
          const taskKey = `${item.positionId}${item.title}`;
          if (item.calmSeconds > 0) {
            newCountdowns[taskKey] = item.calmSeconds;
          }
        });
      }

      // 确保包含拆红包领金币和逛商城领金币任务（如果接口没有返回的话）
      const hasRedPacketTask = dailyTaskList.some(task => task.type === DailyTaskType.RED_PACKET_RAIN);
      const hasMarketTask = dailyTaskList.some(task => task.type === DailyTaskType.MARKET);

      if (!hasRedPacketTask) {
        dailyTaskList.unshift({
          positionId: 1001,
          positionName: 'incentive_welfare',
          title: '拆红包领金币',
          subTitle: '微信点击红包10s，手速即金速！',
          coins: 100,
          extMap: '{}',
          calmSeconds: 0,
          btnText: '去拆包',
          type: DailyTaskType.RED_PACKET_RAIN
        });
      }

      if (!hasMarketTask) {
        dailyTaskList.push({
          positionId: 1002,
          positionName: 'incentive_welfare',
          title: '逛商城领金币',
          subTitle: '清动浏览商品30秒，立得100金币',
          coins: 100,
          extMap: '{}',
          calmSeconds: 0,
          btnText: '去逛逛',
          type: DailyTaskType.MARKET
        });
      }

      set(dailyTaskAtom, {
        loading: false,
        list: dailyTaskList,
        exchangeTasks,
        countdowns: newCountdowns,
        updatingPositions: key ? state.updatingPositions.filter(id => id !== key) : [],
      });
    } catch (error) {
      console.error('更新每日任务失败:', error);
      set(dailyTaskAtom, {
        ...state,
        loading: false,
        updatingPositions: key ? state.updatingPositions.filter(id => id !== key) : [],
      });
    }
  }
);

export const updateCountdownAtom = atom(
  null,
  (get, set, payload: { key: string; countdown: number }) => {
    const state = get(dailyTaskAtom);
    set(dailyTaskAtom, {
      ...state,
      countdowns: {
        ...state.countdowns,
        [payload.key]: payload.countdown,
      },
    });
  }
);