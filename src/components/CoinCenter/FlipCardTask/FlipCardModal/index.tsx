import React, { useEffect, useState, useRef, useCallback } from 'react';
import { View, Modal, Text, Animated, Easing, Dimensions, LayoutChangeEvent } from 'react-native';
import { BetterImage, Touch } from '@xmly/rn-components';
import { getStyles } from './styles';
import { useAtomValue } from 'jotai';
import flipCardModalThemeAtom from './theme';
import xmlog from 'utilsV2/xmlog';
import AlphaVideo from 'components/common/AlphaVideo';
import { useVideoResources } from 'hooks/useVideoResources';
import { videoSources } from './sources';
import ConfirmButton from 'components/CoinCenter/common/ConfirmButton';
import CloseButton from 'components/CoinCenter/common/CloseButton';
import useResponsiveHeight from './userResponsiveHeight';
import { px } from 'utils/px';
// import log from 'utils/log';

// 背景开始视频预计时长（毫秒）
const BACKGROUND_START_DURATION = 2000; // 根据实际视频长度调整
// 预加载背景循环的提前时间（毫秒）
const PRELOAD_LOOP_TIME = 100;
// 标题动画延迟时间（毫秒）- 从视频开始播放计算
const TITLE_ANIMATION_DELAY = 1500;
// 标题动画时长（毫秒）
const TITLE_ANIMATION_DURATION = 500;
// 金额动画延迟时间（毫秒）- 从视频开始播放计算
const VALUE_ANIMATION_DELAY = 1867;
// 金额动画时长（毫秒）
const VALUE_ANIMATION_DURATION = 500;

interface FlipCardModalProps {
  visible: boolean;
  onClose: () => void;
  onFlipNext: () => void;
  onRetry: () => void;
  currentAmount?: number;
}

// 导出预加载资源的hook
export const useFlipCardVideos = () => {
  return useVideoResources(videoSources, { autoLoad: false });
};

const cardTitle = [
  {
    name: '个',
    icon: 'https://imagev2.xmcdn.com/storages/f872-audiofreehighqps/6F/22/GAqhF9kL1nUhAAAEZAOZNbea.png'
  }, {
    name: '十',
    icon: 'https://imagev2.xmcdn.com/storages/e46f-audiofreehighqps/5C/0C/GKwRIMAL1nUhAAADKgOZNbdk.png'
  }, {
    name: '百',
    icon: 'https://imagev2.xmcdn.com/storages/ad97-audiofreehighqps/96/73/GAqh1QQL1nUiAAADdwOZNbgR.png'
  }, {
    name: '千',
    icon: 'https://imagev2.xmcdn.com/storages/40e3-audiofreehighqps/6E/2D/GAqhQ6cL1nUiAAADPwOZNbfQ.png',
    titleIcon: 'https://imagev2.xmcdn.com/storages/2a32-audiofreehighqps/E4/C0/GAqh_aQL1nUiAAAEcwOZNbg8.png',
  }
];

export default function FlipCardModal({
  visible,
  onClose,
  onFlipNext,
  onRetry,
  currentAmount = 0,
}: FlipCardModalProps) {
  const theme = useAtomValue(flipCardModalThemeAtom);
  const styles = getStyles(theme);
  const isMax = `${currentAmount}`.split('')[0] === '9';
  const currentIndex = `${currentAmount}`.length - 1;
  const cardNumber = `${currentAmount}`.split('')[0];
  const title = cardTitle[currentIndex];
  const nextTitle = cardTitle[Math.min(currentIndex + 1, 3)];
  const btnText = currentAmount > 1000 && cardNumber === '9' ? '开心收下' : `继续翻${nextTitle.name}位`;
  const subBtnText = currentAmount > 1000 && cardNumber !== '9' ? '开心收下' : `浏览广告重翻${title.name}位卡`;
  const [containerWidth, setContainerWidth] = useState(Dimensions.get('window').width);
  const { pv, videoHeight, videoWidth } = useResponsiveHeight({
    width: containerWidth,
  });
  // log('debug__screen', Dimensions.get('screen'))
  // log('debug_videoHeight', { videoHeight, videoWidth });

  const [backgroundLoopVisible, setBackgroundLoopVisible] = useState(false);

  // 添加一个定时器引用
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  // 标题动画定时器引用
  const titleTimerRef = useRef<NodeJS.Timeout | null>(null);
  // 金额动画定时器引用
  const valueTimerRef = useRef<NodeJS.Timeout | null>(null);
  // 背景视频开始加载的时间
  const bgStartLoadTimeRef = useRef<number>(0);
  // 组件是否已经卸载的标记
  const isMountedRef = useRef(true);
  // 视频开始播放的时间标记
  const videoStartTimeRef = useRef<number>(0);

  // 创建动画值
  const opacityEasing = Easing.bezier(0.66, 0, 0.34, 1);
  const titleOpacityAnim = useRef(new Animated.Value(0)).current;
  const valueOpacityAnim = useRef(new Animated.Value(0)).current;

  // 保存动画引用
  const titleAnimRef = useRef<Animated.CompositeAnimation | null>(null);
  const valueAnimRef = useRef<Animated.CompositeAnimation | null>(null);

  // 使用 useVideoResources hook 加载视频资源
  const { paths } = useVideoResources(videoSources, {
    autoLoad: visible, // 当弹窗显示时自动加载资源
  });

  useEffect(() => {
    // 组件挂载时设置标记
    isMountedRef.current = true;

    if (visible) {
      // 福利中心-翻卡片-弹窗  控件曝光
      xmlog.event(67704, 'slipPage', { currPage: 'welfareCenter' });
    }

    // 组件卸载时清理
    return () => {
      isMountedRef.current = false;

      // 清除所有定时器
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
      if (titleTimerRef.current) {
        clearTimeout(titleTimerRef.current);
        titleTimerRef.current = null;
      }
      if (valueTimerRef.current) {
        clearTimeout(valueTimerRef.current);
        valueTimerRef.current = null;
      }
    };
  }, [visible]);

  // 安全设置状态的辅助函数
  const safeSetState = useCallback(<T extends any>(setter: React.Dispatch<React.SetStateAction<T>>, value: T) => {
    if (isMountedRef.current) {
      setter(value);
    }
  }, []);

  // 视频开始加载回调
  useEffect(() => {
    // 如果组件已卸载，不执行后续操作
    if (!isMountedRef.current) return;
    if (!paths.flipCard || !visible) return;

    // 记录视频开始播放的时间
    videoStartTimeRef.current = Date.now();

    // 设置标题动画的延迟定时器
    if (titleTimerRef.current) {
      clearTimeout(titleTimerRef.current);
    }

    titleTimerRef.current = setTimeout(() => {
      if (isMountedRef.current) {
        // 开始标题的透明度动画
        titleAnimRef.current = Animated.timing(titleOpacityAnim, {
          toValue: 1,
          duration: TITLE_ANIMATION_DURATION,
          easing: opacityEasing,
          useNativeDriver: true
        });
        titleAnimRef.current.start();
      }
    }, TITLE_ANIMATION_DELAY);

    // 设置金额动画的延迟定时器
    if (valueTimerRef.current) {
      clearTimeout(valueTimerRef.current);
    }

    valueTimerRef.current = setTimeout(() => {
      if (isMountedRef.current) {
        // 开始金额的透明度动画
        valueAnimRef.current = Animated.timing(valueOpacityAnim, {
          toValue: 1,
          duration: VALUE_ANIMATION_DURATION,
          easing: opacityEasing,
          useNativeDriver: true
        });
        valueAnimRef.current.start();
      }
    }, VALUE_ANIMATION_DELAY);

  }, [opacityEasing, titleOpacityAnim, valueOpacityAnim, paths.flipCard, visible]);

  // 背景开始视频加载完成回调
  const handleBgStartLoad = useCallback(() => {
    // 如果组件已卸载，不执行后续操作
    if (!isMountedRef.current) return;

    // 记录背景视频开始加载的时间
    bgStartLoadTimeRef.current = Date.now();

    // 计算预加载时间
    const preloadDelay = Math.max(BACKGROUND_START_DURATION - PRELOAD_LOOP_TIME, 0);

    // 清除可能存在的旧定时器
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // 设置新的定时器，提前预加载BACKGROUND_LOOP
    timerRef.current = setTimeout(() => {
      if (isMountedRef.current) {
        safeSetState(setBackgroundLoopVisible, true);
      }
    }, preloadDelay);
  }, [safeSetState]);

  // 背景视频播放完成回调
  const handleBgVideoComplete = useCallback(() => {
    // 如果组件已卸载，不执行后续操作
    if (!isMountedRef.current) return;

    // 如果还没有显示背景循环视频，立即显示
    if (!backgroundLoopVisible) {
      safeSetState(setBackgroundLoopVisible, true);
    }

    // 清除定时器
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  }, [backgroundLoopVisible, safeSetState]);

  // 当弹窗关闭时重置状态
  useEffect(() => {
    if (!visible) {
      // 停止正在运行的动画
      if (titleAnimRef.current) {
        titleAnimRef.current.stop();
        titleAnimRef.current = null;
      }
      if (valueAnimRef.current) {
        valueAnimRef.current.stop();
        valueAnimRef.current = null;
      }

      if (isMountedRef.current) {
        titleOpacityAnim.setValue(0);
        valueOpacityAnim.setValue(0);
        safeSetState(setBackgroundLoopVisible, false);
      }
      bgStartLoadTimeRef.current = 0;
      videoStartTimeRef.current = 0;

      // 清除所有定时器
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
      if (titleTimerRef.current) {
        clearTimeout(titleTimerRef.current);
        titleTimerRef.current = null;
      }
      if (valueTimerRef.current) {
        clearTimeout(valueTimerRef.current);
        valueTimerRef.current = null;
      }
    }
  }, [visible, titleOpacityAnim, valueOpacityAnim, safeSetState]);

  function clickReport(btnText: string) {
    // 福利中心-翻卡片-弹窗  点击事件
    xmlog.click(67703, 'FlipCardModal', { currPage: 'welfareCenter', Item: btnText });
  }

  function retryBtnOnClick() {
    clickReport(subBtnText);
    if (currentAmount > 1000 && +cardNumber < 9) { // 开心收下
      onFlipNext();
    } else {
      onRetry();
    }
  }

  function btnOnClick() {
    clickReport(btnText);
    if (currentAmount > 1000 && +cardNumber < 9) { // 重翻千位
      onRetry();
    } else {
      onFlipNext();
    }
  }

  function closeBtnOnClick() {
    clickReport('关闭');
    onClose();
  }

  // 渲染视频内容 - flipCard视频与背景视频分开渲染
  const renderFlipCardVideo = () => {
    return paths.flipCard ? (
      <AlphaVideo
        source={paths.flipCard}
        style={[styles.fullVideo]}
        repeat={false}
      />
    ) : null;
  };

  // 渲染背景视频 - 独立于flipCard视频
  const renderBackgroundVideos = () => {
    return (
      <>
        {/* 背景开始视频 */}
        {paths.awardBgStart ? (
          <AlphaVideo
            source={paths.awardBgStart}
            style={[
              styles.fullVideo,
              { opacity: backgroundLoopVisible ? 0 : 1 }
            ]}
            repeat={false}
            onLoad={handleBgStartLoad}
            onComplete={handleBgVideoComplete}
          />
        ) : null}

        {/* 背景循环视频 - 提前加载但开始时透明度为0，需要时再显示 */}
        {paths.awardBg ? (
          <AlphaVideo
            source={paths.awardBg}
            style={[
              styles.fullVideo,
              { opacity: backgroundLoopVisible ? 1 : 0 }
            ]}
            repeat={true}
          />
        ) : null}
      </>
    );
  };

  const handleLayout = useCallback((event: LayoutChangeEvent) => {
    setContainerWidth(event.nativeEvent.layout.width);
  }, []);

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
      onRequestClose={closeBtnOnClick}
    >
      <View style={[styles.container]} onLayout={handleLayout}>
        <Touch style={styles.overlay} onPress={closeBtnOnClick} />
        <CloseButton onPress={onClose} />
        <View style={[styles.videoWrapper, {
          width: videoWidth,
          transform: [
            {
              translateX: -videoWidth / 2
            },
            {
              translateY: -videoHeight / 2
            }
          ]
        }]}>
          {renderBackgroundVideos()}
          {renderFlipCardVideo()}
          <Animated.View style={[styles.content, {
            transform: [
              {
                translateX: -px(62)
              },
              {
                translateY: videoHeight > 1000 ? -pv(220) : -pv(240)
              }
            ],
            opacity: titleOpacityAnim
          }]}>
            <View style={styles.title}>
              <BetterImage
                source={{ uri: 'https://imagev2.xmcdn.com/storages/9b9e-audiofreehighqps/89/F0/GAqh_aQL1jVzAAAK-gOZDOCl.png' }}
                style={[styles.titleIcon]}
                quality={10}
                resizeMode='contain'
              />
              <Text style={[styles.titleText]}>
                {cardNumber}
              </Text>
            </View>
          </Animated.View>
          <Animated.View style={[
            styles.btns,
            {
              opacity: titleOpacityAnim
            },
            {
              transform: [
                {
                  translateX: -px(110)
                }, {
                  translateY: videoHeight > 1000 ? pv(142) : pv(172)
                }
              ],
            }
          ]}>
            <ConfirmButton
              text={btnText}
              onPress={btnOnClick}
              style={styles.button}
            />
            {isMax ? null : (
              <Touch style={[{
                marginTop: pv(23)
              }]} onPress={retryBtnOnClick}>
                <Text style={[styles.retryButtonText]}>{subBtnText}</Text>
              </Touch>
            )}
          </Animated.View>
          <Animated.View style={[
            styles.cardNumber,
            {
              width: videoWidth,
              transform: [
                {
                  translateY: currentIndex === 3 ?
                    -pv(124)
                    :
                    -pv(130)
                }
              ],
            },
            { opacity: valueOpacityAnim }
          ]}>
            {currentIndex === 3 ? (
              <>
                <BetterImage
                  key={title!.titleIcon + '_' + pv(72)}
                  source={{ uri: title.titleIcon }}
                  style={[styles.titleIconK, {
                    width: pv(72)
                  }]}
                  resizeMode='contain'
                />
                <Text
                  numberOfLines={1}
                  style={[styles.cardNumberText, {
                    marginTop: pv(18),
                    marginBottom: pv(14),
                    fontSize: pv(55),
                    lineHeight: pv(67),
                  }]}>{currentAmount}</Text>
                <BetterImage
                  key={title.icon + '_' + pv(64)}
                  source={{ uri: title.icon }}
                  style={[styles.cardIconK, {
                    width: pv(64)
                  }]}
                  resizeMode='contain'
                />
              </>
            ) : (
              <>
                <Text style={[styles.cardNumberText, {
                  fontSize: pv(100),
                  lineHeight: pv(122),
                }]}>{cardNumber}</Text>
                <BetterImage source={{ uri: title.icon }} style={[styles.cardIcon, { width: px(71), marginTop: pv(1.14) }]} resizeMode='contain' />
              </>
            )}
          </Animated.View>
        </View>
      </View>
    </Modal>
  );
} 