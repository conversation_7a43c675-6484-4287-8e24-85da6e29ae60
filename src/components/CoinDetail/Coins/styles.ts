import { Platform, StyleSheet } from 'react-native';
import { px } from 'utils/px';
import { darkTheme } from './theme';

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    alignItems: 'center',
    flexDirection: 'column',
    justifyContent: 'center',
    marginTop: px(40),
    marginBottom: px(45),
  },
  title: {
    fontSize: px(45),
    lineHeight: px(55),
    fontFamily: 'XmlyNumber',
    fontWeight: '500',
    color: theme.titleColor,
  },
  coins: {
    fontSize: px(45),
    fontWeight: Platform.select({ ios: '600', android: 'bold' }),
    color: theme.coinsColor,
    marginTop: px(4),
    fontFamily: 'XmlyNumber',
    lineHeight: px(55),
  },
  coinsWrapper: {
    height: px(55),
    justifyContent: 'center',
    alignItems: 'center',
  },
  coinsIconWrapper: {
    position: 'absolute',
    flexDirection: 'row',
    height: px(55),
    left: 0,
    transform: [{ translateX: px(-52) }],
    justifyContent: 'flex-end',
  },
  coinsIcon: {
    width: px(52),
    height: px(52),
    top: Platform.select({ ios: 4, android: 0 }),
  },
  ruleWrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: px(5),
  },
  ruleText: {
    fontSize: px(12),
    color: theme.ruleTextColor,
  },
}); 