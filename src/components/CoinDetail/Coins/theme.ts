import { atom } from 'jotai';
import { themeAtom } from 'atom/theme';

export const darkTheme = {
  backgroundColor: '#1F1F1F',
  titleColor: 'rgba(255, 255, 255, 0.55)',
  coinsColor: '#FFFFFF',
  ruleTextColor: '#66666B',
};

export const lightTheme = {
  backgroundColor: '#FFFFFF',
  titleColor: 'rgba(36, 0, 0, 0.55)',
  coinsColor: '#240000',
  ruleTextColor: '#ACACAF',
};

export const coinsThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
});

export default coinsThemeAtom; 