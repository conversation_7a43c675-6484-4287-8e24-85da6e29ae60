import React from 'react';
import { getStyles } from './styles';
import { BetterImage } from '@xmly/rn-components';
import { useAtomValue } from 'jotai';
import { themeAtom } from 'atom/theme';

export default function TopBg() {
  const theme = useAtomValue(themeAtom);
  const bg = theme === 'dark' ? 'https://imagev2.xmcdn.com/storages/9957-audiofreehighqps/AB/B4/GAqhfD0LtawWAAAkYgOEOTHD.jpg' : 'https://imagev2.xmcdn.com/storages/8dc7-audiofreehighqps/A8/59/GAqhntALtawWAAA-qQOEOTHo.jpg';
  const styles = getStyles();
  return <BetterImage source={{ uri: bg }} key={bg} style={styles.container} />;
} 