import React from "react";
import { ViewStyle, StyleProp, View, requireNativeComponent } from "react-native";

interface InspireAdViewParams {
    positionName: string
    slotId: number
    sourceName: string
    rewardCoin: number
    pointerEvents?: 'none' | 'auto' | 'box-none' | 'box-only';
    repeat?: boolean;
    muted?: boolean;
    style?: StyleProp<ViewStyle>;
    onAdClick?: () => void
    onHeightChange?: (e: any) => void
    onRewardSuccess?: (adId: number, adResponseId: number) => void
    onRewardFail?: (msg: string) => void
  }
  
 const PlatformInspireAdView = requireNativeComponent('InspireAdView')
 export default function NativeInspireAdView(props: InspireAdViewParams) {
    const {
      positionName,
      slotId,
      sourceName,
      rewardCoin,
      style,
      onAdClick,
      onHeightChange,
    } = props;

    console.log('NativeInspireAdView props:>>>', JSON.stringify(props));

    console.log('PlatformInspireAdView: :>>>>>>>>>>>>>>>>>>>>>>>>>>>', PlatformInspireAdView, typeof PlatformInspireAdView);

    if (PlatformInspireAdView === undefined) {
      console.error('PlatformInspireAdView: :>>>', typeof PlatformInspireAdView);
        return null;
    }

    const options = {
      slot_id: typeof slotId === 'number' ? slotId : 308,
      positionName: positionName || 'fuliyehuanduanguanggao',
      rewardCoin: rewardCoin || 0,
      sourceName
    };

    console.log('NativeInspireAdView options:>>>', JSON.stringify(options));  
    
    return (
    <View 
      pointerEvents="box-none"
      style={[style]}
    //   onStartShouldSetResponder={() => true}
    >
        <PlatformInspireAdView
          //@ts-ignore  
          onAdClick={onAdClick}
          onHeightChange={onHeightChange}
        //   onRewardSuccess={handleRewardSuccess}
        //   onRewardFail={handleRewardFail}
          bindData={options}
          style={{ width: '100%', height: '100%' }}
          pointerEvents="box-only"
        />
    </View>
    );
  }
  