# 调试版本说明

## 如果依赖安装有问题，可以使用以下简化版本进行测试

### 1. 简化的 ExchangeTaskItem 组件

```tsx
// src/components/CoinCenter/DailyTask/ExchangeTaskItem/index.tsx
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';

interface ExchangeTaskItemProps {
  item: any;
  isFirst?: boolean;
  loading?: boolean;
  index: number;
}

export default function ExchangeTaskItem({ item, isFirst, loading, index }: ExchangeTaskItemProps) {
  const handlePress = () => {
    console.log('换量任务点击:', item.title);
    // 这里可以添加实际的任务执行逻辑
  };

  return (
    <View style={{
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingVertical: 16,
      paddingHorizontal: 16,
      borderBottomWidth: 1,
      borderBottomColor: '#f0f0f0',
    }}>
      <View style={{ flex: 1, marginRight: 12 }}>
        <Text style={{ fontSize: 16, fontWeight: '500', color: '#333', marginBottom: 4 }}>
          {item.title}
        </Text>
        <Text style={{ fontSize: 14, color: '#666', marginBottom: 4 }}>
          {item.desc}
        </Text>
        {item.worth && (
          <Text style={{ fontSize: 12, color: '#FF6B35', fontWeight: '500' }}>
            +{item.worth}积分
          </Text>
        )}
      </View>
      <TouchableOpacity
        style={{
          minWidth: 80,
          height: 32,
          borderRadius: 16,
          backgroundColor: '#FF4444',
          justifyContent: 'center',
          alignItems: 'center',
        }}
        onPress={handlePress}
        disabled={loading}
      >
        <Text style={{ color: 'white', fontSize: 14 }}>
          {item.status === 'finished' ? '去领取' : '去完成'}
        </Text>
      </TouchableOpacity>
    </View>
  );
}
```

### 2. 简化的主组件修改

```tsx
// src/components/CoinCenter/DailyTask/index.tsx 的关键部分
import ExchangeTaskItem from './ExchangeTaskItem';

// 在组件中添加模拟数据用于测试
const mockExchangeTasks = [
  {
    id: 1001,
    title: '去中国移动领话费流量',
    desc: '移动用户专享福利',
    worth: 50,
    status: 'unfinished'
  },
  {
    id: 1002,
    title: '去快手看短视频',
    desc: '每天都能领金币',
    worth: 50,
    status: 'unfinished'
  }
];

// 在渲染部分添加
{mockExchangeTasks.map((item, index) => (
  <ExchangeTaskItem
    key={`mock_${item.id}`}
    index={list.length + index}
    item={item}
    isFirst={index === 0 && list.length === 0}
    loading={loading}
  />
))}
```

### 3. 测试步骤

1. 先使用模拟数据验证UI显示是否正确
2. 确认点击事件能正常触发
3. 验证样式在不同主题下的表现
4. 逐步替换为真实的数据源和逻辑

### 4. 常见问题排查

1. **组件不显示**: 检查导入路径是否正确
2. **样式异常**: 确认主题配置是否正确加载
3. **点击无响应**: 检查事件处理函数是否正确绑定
4. **数据不更新**: 确认Redux状态是否正确连接

这个简化版本可以帮助您快速验证基本功能，然后再逐步完善。
