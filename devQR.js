const os = require('os')

const baseURL = `iting://open?msg_type=94&__debug=1`
const bundleName = require('./app.json').name

function getIPAddress() {
  const interfaces = os.networkInterfaces()
  for (let devName in interfaces) {
    const iface = interfaces[devName]
    for (let i = 0; i < iface.length; i++) {
      let alias = iface[i]
      if (
        alias.family === 'IPv4' &&
        alias.address !== '127.0.0.1' &&
        !alias.internal
      ) {
        return alias.address
      }
    }
  }
}

function getProjectDevURL(bundleName, port = 8081, args) {
  const LOCAL_IP = getIPAddress()
  const ip = `${LOCAL_IP}:${port}`
  const argsArr = []
  for (let key in args) {
    argsArr.push(`${key}=${args[key]}`)
  }
  return `${baseURL}&bundle=${bundleName}&ip=${ip}&__ip=${ip}${
    argsArr.length > 0 ? `&${argsArr.join('&')}` : ''
  }`
}

function run() {
  const devURL = getProjectDevURL(bundleName)

  console.log('')
  console.log('🚀 React Native 开发服务器启动中...')
  console.log(`📱 开发调试地址: ${devURL}`)
  console.log('')

  // 生成在线QR码链接
  const qrCodeURL = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(devURL)}`

  console.log('📱 扫描二维码进行调试:')
  console.log(`🔗 QR码链接: ${qrCodeURL}`)
  console.log('')
  console.log('💡 使用方法:')
  console.log('1. 复制上面的QR码链接到浏览器打开')
  console.log('2. 用手机扫描显示的二维码')
  console.log('3. 或者直接在喜马拉雅APP中输入调试地址')
  console.log('')
  console.log('🔄 如果需要重新生成QR码，请访问:')
  console.log(`   https://www.qr-code-generator.com/`)
  console.log(`   然后输入: ${devURL}`)
  console.log('')
}

run()
