#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🤖 启动 Android 应用...');
console.log('');

// 检查 Android 环境
function checkAndroidEnvironment() {
  return new Promise((resolve, reject) => {
    exec('adb devices', (error, stdout, stderr) => {
      if (error) {
        console.error('❌ ADB 未找到，请确保 Android SDK 已安装并配置环境变量');
        console.log('💡 请检查以下环境变量：');
        console.log('   - ANDROID_HOME');
        console.log('   - PATH 包含 $ANDROID_HOME/platform-tools');
        reject(error);
        return;
      }
      
      const devices = stdout.split('\n').filter(line => 
        line.includes('device') && !line.includes('List of devices')
      );
      
      if (devices.length === 0) {
        console.error('❌ 未找到连接的 Android 设备或模拟器');
        console.log('💡 请确保：');
        console.log('   1. Android 设备已连接并开启 USB 调试');
        console.log('   2. 或者 Android 模拟器正在运行');
        console.log('   3. 运行 adb devices 查看设备状态');
        reject(new Error('No devices found'));
        return;
      }
      
      console.log('✅ 找到 Android 设备:');
      devices.forEach(device => {
        console.log(`   📱 ${device.trim()}`);
      });
      console.log('');
      resolve();
    });
  });
}

// 构建 Android 应用
function buildAndroidApp() {
  return new Promise((resolve, reject) => {
    console.log('🔨 构建 Android 应用...');
    
    const gradlew = process.platform === 'win32' ? 'gradlew.bat' : './gradlew';
    const androidDir = path.join(__dirname, 'android');
    
    if (!fs.existsSync(path.join(androidDir, 'gradlew'))) {
      console.error('❌ 未找到 gradlew，请确保在 React Native 项目根目录');
      reject(new Error('gradlew not found'));
      return;
    }
    
    const buildProcess = spawn(gradlew, ['assembleDebug'], {
      cwd: androidDir,
      stdio: 'inherit',
      shell: true
    });
    
    buildProcess.on('error', (error) => {
      console.error('❌ 构建失败:', error.message);
      reject(error);
    });
    
    buildProcess.on('exit', (code) => {
      if (code === 0) {
        console.log('✅ Android 应用构建成功');
        resolve();
      } else {
        console.error(`❌ 构建失败，退出代码: ${code}`);
        reject(new Error(`Build failed with code ${code}`));
      }
    });
  });
}

// 安装应用到设备
function installApp() {
  return new Promise((resolve, reject) => {
    console.log('📲 安装应用到设备...');
    
    const apkPath = path.join(__dirname, 'android/app/build/outputs/apk/debug/app-debug.apk');
    
    if (!fs.existsSync(apkPath)) {
      console.error('❌ 未找到 APK 文件:', apkPath);
      reject(new Error('APK not found'));
      return;
    }
    
    const installProcess = spawn('adb', ['install', '-r', apkPath], {
      stdio: 'inherit'
    });
    
    installProcess.on('error', (error) => {
      console.error('❌ 安装失败:', error.message);
      reject(error);
    });
    
    installProcess.on('exit', (code) => {
      if (code === 0) {
        console.log('✅ 应用安装成功');
        resolve();
      } else {
        console.error(`❌ 安装失败，退出代码: ${code}`);
        reject(new Error(`Install failed with code ${code}`));
      }
    });
  });
}

// 启动应用
function startApp() {
  return new Promise((resolve, reject) => {
    console.log('🚀 启动应用...');
    
    // 从 package.json 或 app.json 获取包名
    let packageName = 'com.rn_credit_center'; // 默认包名
    
    try {
      const appJson = require('./app.json');
      if (appJson.name) {
        packageName = `com.${appJson.name}`;
      }
    } catch (e) {
      // 使用默认包名
    }
    
    const startProcess = spawn('adb', [
      'shell',
      'am',
      'start',
      '-n',
      `${packageName}/.MainActivity`
    ], {
      stdio: 'inherit'
    });
    
    startProcess.on('error', (error) => {
      console.error('❌ 启动失败:', error.message);
      reject(error);
    });
    
    startProcess.on('exit', (code) => {
      if (code === 0) {
        console.log('✅ 应用启动成功');
        console.log('');
        console.log('📱 应用已在设备上运行');
        console.log('🔄 Metro 服务器应该已经在运行，如果没有请运行: node simple-start.js');
        resolve();
      } else {
        console.error(`❌ 启动失败，退出代码: ${code}`);
        reject(new Error(`Start failed with code ${code}`));
      }
    });
  });
}

// 主函数
async function main() {
  try {
    await checkAndroidEnvironment();
    await buildAndroidApp();
    await installApp();
    await startApp();
    
    console.log('');
    console.log('🎉 Android 应用启动完成！');
    console.log('');
    console.log('📋 下一步：');
    console.log('1. 在设备上打开喜马拉雅APP');
    console.log('2. 导航到福利中心页面');
    console.log('3. 查看每日任务模块中的换量任务');
    console.log('4. 测试点击交互功能');
    
  } catch (error) {
    console.error('');
    console.error('❌ 启动过程中出现错误:', error.message);
    console.log('');
    console.log('💡 故障排除建议：');
    console.log('1. 确保 Android SDK 已正确安装');
    console.log('2. 确保设备已连接并开启 USB 调试');
    console.log('3. 尝试手动运行: cd android && ./gradlew assembleDebug');
    console.log('4. 检查 Metro 服务器是否正在运行');
    process.exit(1);
  }
}

main();
