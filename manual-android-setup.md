# 手动 Android 构建和启动指南

## 前提条件检查

### 1. 检查 Android 设备连接
```bash
adb devices
```
应该显示您的设备，如：
```
List of devices attached
ABC123DEF456    device
```

### 2. 检查 Android SDK 环境
```bash
echo $ANDROID_HOME
echo $PATH | grep android
```

## 手动构建步骤

### 步骤 1: 确保 Metro 服务器运行
在一个终端窗口中运行：
```bash
node simple-start.js
```
或者：
```bash
npx metro start --reset-cache
```

### 步骤 2: 构建 Android 应用
在另一个终端窗口中：
```bash
cd android
./gradlew assembleDebug
```

### 步骤 3: 安装应用到设备
```bash
adb install -r app/build/outputs/apk/debug/app-debug.apk
```

### 步骤 4: 启动应用
```bash
# 获取包名（通常是 com.项目名）
adb shell pm list packages | grep credit

# 启动应用（替换为实际包名）
adb shell am start -n com.rn_credit_center/.MainActivity
```

## 故障排除

### 问题 1: gradlew 权限错误
```bash
chmod +x android/gradlew
```

### 问题 2: 构建失败
```bash
cd android
./gradlew clean
./gradlew assembleDebug
```

### 问题 3: 设备未授权
- 在手机上点击"允许 USB 调试"
- 勾选"始终允许来自这台计算机"

### 问题 4: 应用启动失败
```bash
# 查看应用日志
adb logcat | grep ReactNative

# 或者查看所有日志
adb logcat
```

## 验证功能

### 1. 应用启动后
1. 打开喜马拉雅APP
2. 导航到福利中心
3. 滚动到每日任务模块
4. 查看是否显示了换量任务

### 2. 测试交互
1. 点击换量任务按钮
2. 查看控制台日志输出
3. 验证按钮状态变化

### 3. 调试日志
在 Metro 终端中应该能看到：
```
DailyTask mounted, mock exchange tasks: [...]
换量任务点击: 去中国移动领话费流量
```
