#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 启动简化版 React Native 开发服务器...');
console.log('');

// 检查是否有 Metro 配置
const metroConfigPath = path.join(__dirname, 'metro.config.js');
if (!fs.existsSync(metroConfigPath)) {
  console.log('📝 创建基本的 Metro 配置...');
  const metroConfig = `
const { getDefaultConfig } = require('metro-config');

module.exports = (async () => {
  const {
    resolver: { sourceExts, assetExts },
  } = await getDefaultConfig();
  return {
    transformer: {
      babelTransformerPath: require.resolve('metro-react-native-babel-transformer'),
    },
    resolver: {
      assetExts: assetExts.filter(ext => ext !== 'svg'),
      sourceExts: [...sourceExts, 'svg'],
    },
  };
})();
`;
  fs.writeFileSync(metroConfigPath, metroConfig);
}

// 尝试启动 Metro
console.log('⚡ 启动 Metro bundler...');

// 使用 npx 启动 Metro
const child = spawn('npx', ['metro', 'start', '--reset-cache'], {
  stdio: 'inherit',
  shell: true,
  cwd: __dirname
});

child.on('error', (error) => {
  console.error('❌ Metro 启动失败:', error.message);
  console.log('');
  console.log('💡 请尝试以下步骤：');
  console.log('1. 确保已安装 Node.js 和 npm');
  console.log('2. 运行: npm install -g metro');
  console.log('3. 或者运行: npx metro start');
  console.log('');
});

child.on('exit', (code) => {
  if (code !== 0) {
    console.log(`❌ Metro 退出，代码: ${code}`);
  }
});

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n👋 正在关闭开发服务器...');
  child.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  child.kill('SIGTERM');
  process.exit(0);
});
