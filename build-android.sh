#!/bin/bash

echo "🤖 构建和安装 Android 应用"
echo ""

# 检查设备连接
echo "📱 检查 Android 设备..."
DEVICES=$(adb devices | grep -v "List of devices" | grep "device" | wc -l)

if [ $DEVICES -eq 0 ]; then
    echo "❌ 未找到连接的 Android 设备"
    echo "💡 请确保："
    echo "   1. Android 设备已连接并开启 USB 调试"
    echo "   2. 或者 Android 模拟器正在运行"
    echo "   3. 运行 'adb devices' 查看设备状态"
    exit 1
fi

echo "✅ 找到 $DEVICES 个设备"
adb devices
echo ""

# 检查 Metro 服务器
echo "🔄 检查 Metro 服务器..."
if ! curl -s http://localhost:8081/status > /dev/null 2>&1; then
    echo "⚠️  Metro 服务器未运行"
    echo "💡 请在另一个终端运行: node simple-start.js"
    echo "   或者: npx metro start"
    echo ""
    read -p "Metro 服务器启动后，按 Enter 继续..."
fi

# 构建应用
echo "🔨 构建 Android 应用..."
cd android

if [ ! -f "./gradlew" ]; then
    echo "❌ 未找到 gradlew 文件"
    echo "💡 请确保在 React Native 项目根目录运行此脚本"
    exit 1
fi

# 确保 gradlew 有执行权限
chmod +x ./gradlew

echo "开始构建..."
./gradlew assembleDebug

if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    echo "💡 尝试清理后重新构建:"
    echo "   cd android && ./gradlew clean && ./gradlew assembleDebug"
    exit 1
fi

echo "✅ 构建成功"

# 安装应用
echo ""
echo "📲 安装应用到设备..."
APK_PATH="app/build/outputs/apk/debug/app-debug.apk"

if [ ! -f "$APK_PATH" ]; then
    echo "❌ 未找到 APK 文件: $APK_PATH"
    exit 1
fi

adb install -r "$APK_PATH"

if [ $? -ne 0 ]; then
    echo "❌ 安装失败"
    echo "💡 尝试卸载后重新安装:"
    echo "   adb uninstall com.rn_credit_center"
    echo "   adb install app/build/outputs/apk/debug/app-debug.apk"
    exit 1
fi

echo "✅ 安装成功"

# 启动应用
echo ""
echo "🚀 启动应用..."

# 尝试不同的包名
PACKAGE_NAMES=("com.rn_credit_center" "com.xmly.rn_credit_center" "com.ximalaya.rn_credit_center")

for PACKAGE in "${PACKAGE_NAMES[@]}"; do
    echo "尝试启动包: $PACKAGE"
    adb shell am start -n "$PACKAGE/.MainActivity" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✅ 应用启动成功"
        break
    fi
done

echo ""
echo "🎉 Android 应用部署完成！"
echo ""
echo "📋 下一步："
echo "1. 在设备上查看应用是否已启动"
echo "2. 导航到福利中心页面"
echo "3. 查看每日任务模块中的换量任务"
echo "4. 测试点击交互功能"
echo ""
echo "🔍 如果应用未自动启动，请手动在设备上打开应用"
