const fs = require('fs');
const path = require('path');

// 检查组件文件是否存在和基本结构
function testComponent() {
  console.log('🧪 测试组件结构...\n');
  
  const files = [
    'src/components/CoinCenter/DailyTask/index.tsx',
    'src/components/CoinCenter/DailyTask/ExchangeTaskItem/index.tsx',
    'src/components/CoinCenter/DailyTask/ExchangeTaskItem/styles.ts',
    'src/components/CoinCenter/DailyTask/ExchangeTaskItem/theme.ts'
  ];
  
  files.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file} - 文件存在`);
      
      const content = fs.readFileSync(file, 'utf8');
      
      // 检查基本结构
      if (file.includes('index.tsx')) {
        if (content.includes('export default')) {
          console.log(`   ✅ 包含默认导出`);
        } else {
          console.log(`   ❌ 缺少默认导出`);
        }
        
        if (content.includes('React')) {
          console.log(`   ✅ 导入了React`);
        } else {
          console.log(`   ❌ 未导入React`);
        }
      }
      
      if (file.includes('styles.ts')) {
        if (content.includes('StyleSheet')) {
          console.log(`   ✅ 包含StyleSheet`);
        } else {
          console.log(`   ❌ 缺少StyleSheet`);
        }
      }
      
      if (file.includes('theme.ts')) {
        if (content.includes('atom')) {
          console.log(`   ✅ 包含atom定义`);
        } else {
          console.log(`   ❌ 缺少atom定义`);
        }
      }
    } else {
      console.log(`❌ ${file} - 文件不存在`);
    }
    console.log('');
  });
  
  // 检查主组件的关键功能
  console.log('🔍 检查主组件功能...\n');
  
  const mainComponent = 'src/components/CoinCenter/DailyTask/index.tsx';
  if (fs.existsSync(mainComponent)) {
    const content = fs.readFileSync(mainComponent, 'utf8');
    
    if (content.includes('ExchangeTaskItem')) {
      console.log('✅ 主组件导入了ExchangeTaskItem');
    } else {
      console.log('❌ 主组件未导入ExchangeTaskItem');
    }
    
    if (content.includes('mockExchangeTasks')) {
      console.log('✅ 包含测试数据');
    } else {
      console.log('❌ 缺少测试数据');
    }
    
    if (content.includes('map')) {
      console.log('✅ 包含列表渲染逻辑');
    } else {
      console.log('❌ 缺少列表渲染逻辑');
    }
  }
  
  console.log('\n🎯 测试完成！');
  console.log('\n📋 下一步建议：');
  console.log('1. 启动React Native开发服务器');
  console.log('2. 在模拟器中查看福利中心页面');
  console.log('3. 验证换量任务是否正确显示');
  console.log('4. 测试点击交互是否正常');
  console.log('5. 检查控制台日志输出');
}

testComponent();
