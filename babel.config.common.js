const options = []
const bigScreenFuncName = ['xmUIFix', 'xmUIPic', 'xm<PERSON><PERSON><PERSON>', 'xmUIIcon', 'xmUIHSpace', 'xmUIVSpace', 'xmUI<PERSON>le']

module.exports = {
  presets: [],
  plugins: [
    // 临时注释掉 import 插件以解决依赖问题
    // [
    //   'import',
    //   {
    //     libraryName: '@xmly/rn-sdk',
    //     customName: (name, file) => {
    //       const filename = file.opts.filename
    //       if (name === 'XMAppVersionHelper') {
    //         return `@xmly/rn-sdk/dist/AppVersionManager/utils/${name}`
    //       }
    //       if (name === 'WithXMAppVersion') {
    //         return `@xmly/rn-sdk/dist/AppVersionManager/${name}`
    //       }
    //       if (name === 'RNOpen') {
    //         return `@xmly/rn-sdk/dist/Page/Scheme/${name}`
    //       }
    //       return `@xmly/rn-sdk/dist/${name}`
    //     },
    //     transformToDefaultImport: false,
    //     camel2DashComponentName: false,
    //   },
    //   '@xmly/rn-sdk',
    // ],
    // [
    //   'import',
    //   {
    //     libraryName: '@xmly/rn-sdk',
    //     libraryDirectory: 'dist',
    //     transformToDefaultImport: false,
    //     camel2DashComponentName: false,
    //     customName: (name, file) => {
    //       if (name === 'XMAppVersionHelper') {
    //         return `@xmly/rn-sdk/dist/AppVersionManager/utils/${name}`
    //       }
    //       if (name === 'WithXMAppVersion' || name === 'XMAppVersionProvider') {
    //         return `@xmly/rn-sdk/dist/AppVersionManager/${name}`
    //       }
    //       if (name === 'RNOpen' || name === 'Open' || name === 'ProductType') {
    //         return '@xmly/rn-sdk/dist/Page/index'
    //       }
    //       if (name === 'RootViewManager') {
    //         return `@xmly/rn-sdk/dist/RootView/${name}`
    //       }
    //       if (name === 'getAppRequestTimeApm') {
    //         return `@xmly/rn-sdk/dist/AppRequestTimeApm/index`
    //       }
    //       if (name === 'withDarkMode') {
    //         return `@xmly/rn-sdk/dist/DarkMode/withDarkMode`
    //       }
    //       return `@xmly/rn-sdk/dist/${name}`
    //     },
    //   },
    //   '@xmly/rn-sdk',
    // ],
    // [
    //   'import',
    //   {
    //     libraryName: '@xmly/rn-utils',
    //     libraryDirectory: 'dist',
    //     transformToDefaultImport: false,
    //     camel2DashComponentName: false,
    //     customName: (name, file) => {
    //       if (bigScreenFuncName.includes(name)) {
    //         return `@xmly/rn-utils/dist/bigScreen`
    //       }

    //       if (name === 'isAndroid' || name === 'isIOS') {
    //         return `@xmly/rn-utils/dist/device`
    //       }

    //       return `@xmly/rn-utils/dist/${name}`
    //     },
    //   },
    //   '@xmly/rn-utils',
    // ],
    // [
    //   'import',
    //   {
    //     libraryName: '@xmly/rn-components',
    //     libraryDirectory: 'dist',
    //     transformToDefaultImport: false,
    //     camel2DashComponentName: false,
    //     customName: (name, file) => {
    //       if (name === 'XMAppVersionHelper') {
    //         return `@xmly/rn-sdk/dist/AppVersionManager/utils/${name}`
    //       }
    //       // if (name === 'WithXMAppVersion') {
    //       //   return `@xmly/rn-sdk/dist/AppVersionManager/${name}`
    //       // }
    //       // if (name === 'RNOpen') {
    //       //   return `@xmly/rn-sdk/dist/Page/Scheme/${name}`
    //       // }
    //       // return `@xmly/rn-sdk/dist/${name}`
    //       return `@xmly/rn-components/dist/${name}`
    //     },
    //   },
    //   '@xmly/rn-components',
    // ],
  ],
}
