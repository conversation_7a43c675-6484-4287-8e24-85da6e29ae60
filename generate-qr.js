#!/usr/bin/env node

const os = require('os');
const { spawn } = require('child_process');

// 获取本机IP地址
function getIPAddress() {
  const interfaces = os.networkInterfaces();
  for (let devName in interfaces) {
    const iface = interfaces[devName];
    for (let i = 0; i < iface.length; i++) {
      let alias = iface[i];
      if (
        alias.family === 'IPv4' &&
        alias.address !== '127.0.0.1' &&
        !alias.internal
      ) {
        return alias.address;
      }
    }
  }
  return 'localhost';
}

// 生成调试URL
function generateDebugURL() {
  const ip = getIPAddress();
  const port = 8081; // Metro 默认端口
  const bundleName = require('./app.json').name;
  
  // 喜马拉雅APP的调试URL格式
  const debugURL = `iting://open?msg_type=94&__debug=1&bundleName=${bundleName}&host=${ip}&port=${port}`;
  
  return {
    debugURL,
    metroURL: `http://${ip}:${port}`,
    ip,
    port,
    bundleName
  };
}

// 生成ASCII艺术QR码（简化版）
function generateASCIIQR(url) {
  const size = 25;
  const qr = [];
  
  // 简化的QR码模式（仅用于演示）
  for (let i = 0; i < size; i++) {
    let row = '';
    for (let j = 0; j < size; j++) {
      // 简单的伪随机模式
      const hash = (i * 31 + j * 17 + url.length) % 4;
      row += hash < 2 ? '██' : '  ';
    }
    qr.push(row);
  }
  
  return qr;
}

// 尝试在浏览器中打开QR码
function openQRInBrowser(url) {
  const qrURL = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(url)}`;
  
  const commands = {
    darwin: 'open',
    win32: 'start',
    linux: 'xdg-open'
  };
  
  const command = commands[process.platform];
  if (command) {
    spawn(command, [qrURL], { detached: true, stdio: 'ignore' });
    return true;
  }
  return false;
}

// 主函数
function main() {
  console.log('📱 生成喜马拉雅APP调试二维码');
  console.log('═'.repeat(50));
  
  const { debugURL, metroURL, ip, port, bundleName } = generateDebugURL();
  
  console.log(`📦 Bundle名称: ${bundleName}`);
  console.log(`🌐 本机IP: ${ip}`);
  console.log(`🔌 Metro端口: ${port}`);
  console.log(`🔗 Metro地址: ${metroURL}`);
  console.log('');
  
  console.log('🎯 调试地址:');
  console.log(`${debugURL}`);
  console.log('');
  
  // 生成在线QR码链接
  const qrURL = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(debugURL)}`;
  
  console.log('📱 二维码链接:');
  console.log(`${qrURL}`);
  console.log('');
  
  // 尝试自动打开浏览器
  console.log('🚀 尝试在浏览器中打开二维码...');
  const opened = openQRInBrowser(debugURL);
  
  if (opened) {
    console.log('✅ 已在浏览器中打开二维码');
  } else {
    console.log('❌ 无法自动打开浏览器');
  }
  
  console.log('');
  console.log('📋 使用说明:');
  console.log('1. 确保手机和电脑在同一WiFi网络');
  console.log('2. 启动Metro服务器: yarn start 或 node simple-start.js');
  console.log('3. 复制上面的二维码链接到浏览器打开');
  console.log('4. 用喜马拉雅APP扫描二维码');
  console.log('5. 或者在APP中手动输入调试地址');
  console.log('');
  
  console.log('🔧 故障排除:');
  console.log('• 如果扫码失败，检查网络连接');
  console.log('• 确保Metro服务器正在运行');
  console.log('• 尝试重启APP和Metro服务器');
  console.log('• 检查防火墙设置');
  console.log('');
  
  // 生成简化的ASCII QR码用于终端显示
  console.log('📟 终端二维码 (仅供参考):');
  const asciiQR = generateASCIIQR(debugURL);
  asciiQR.forEach(row => console.log(row));
  console.log('');
  
  console.log('💡 提示: 上面的ASCII码仅供参考，请使用浏览器中的真实二维码');
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  generateDebugURL,
  openQRInBrowser,
  main
};
