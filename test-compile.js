const fs = require('fs');
const path = require('path');

// 简单的语法检查
function checkSyntax(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查基本的语法错误
    const issues = [];
    
    // 检查括号匹配
    const openBrackets = (content.match(/\{/g) || []).length;
    const closeBrackets = (content.match(/\}/g) || []).length;
    if (openBrackets !== closeBrackets) {
      issues.push(`Bracket mismatch: ${openBrackets} open, ${closeBrackets} close`);
    }
    
    // 检查导入语句
    const imports = content.match(/import .* from .*/g) || [];
    imports.forEach((imp, index) => {
      if (!imp.endsWith(';')) {
        issues.push(`Line ${index + 1}: Import statement missing semicolon: ${imp}`);
      }
    });
    
    return issues;
  } catch (error) {
    return [`Error reading file: ${error.message}`];
  }
}

// 检查我们修改的文件
const filesToCheck = [
  'src/components/CoinCenter/DailyTask/index.tsx',
  'src/components/CoinCenter/DailyTask/ExchangeTaskItem/index.tsx',
  'src/components/CoinCenter/DailyTask/ExchangeTaskItem/styles.ts',
  'src/components/CoinCenter/DailyTask/ExchangeTaskItem/theme.ts'
];

console.log('Checking syntax for modified files...\n');

filesToCheck.forEach(file => {
  console.log(`Checking ${file}:`);
  const issues = checkSyntax(file);
  if (issues.length === 0) {
    console.log('  ✅ No syntax issues found');
  } else {
    console.log('  ❌ Issues found:');
    issues.forEach(issue => console.log(`    - ${issue}`));
  }
  console.log('');
});
